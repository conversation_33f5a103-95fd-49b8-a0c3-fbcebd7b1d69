<?php
/**
 * Property Model
 * Handles property data and operations
 */

class Property extends Model {
    protected $table = 'properties';
    protected $fillable = [
        'tenant_id', 'title', 'description', 'type', 'purpose', 'status', 'price',
        'area', 'bedrooms', 'bathrooms', 'floor', 'parking', 'furnished',
        'address', 'city', 'district', 'latitude', 'longitude', 'features',
        'images', 'documents', 'owner_name', 'owner_phone', 'owner_email',
        'commission_rate', 'commission_amount', 'is_featured', 'created_by', 'updated_by'
    ];
    protected $timestamps = true;
    protected $softDeletes = true;
    
    /**
     * Get properties by status
     */
    public function getByStatus($status, $tenantId = null) {
        $tenantId = $tenantId ?: $this->getCurrentTenantId();
        
        $sql = "SELECT * FROM {$this->table} 
                WHERE status = ? AND tenant_id = ? AND deleted_at IS NULL 
                ORDER BY created_at DESC";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$status, $tenantId]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get properties by type
     */
    public function getByType($type, $tenantId = null) {
        $tenantId = $tenantId ?: $this->getCurrentTenantId();
        
        $sql = "SELECT * FROM {$this->table} 
                WHERE type = ? AND tenant_id = ? AND deleted_at IS NULL 
                ORDER BY created_at DESC";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$type, $tenantId]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get featured properties
     */
    public function getFeatured($tenantId = null, $limit = 10) {
        $tenantId = $tenantId ?: $this->getCurrentTenantId();
        
        $sql = "SELECT * FROM {$this->table} 
                WHERE is_featured = 1 AND tenant_id = ? AND deleted_at IS NULL 
                ORDER BY created_at DESC LIMIT ?";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$tenantId, $limit]);
        return $stmt->fetchAll();
    }
    
    /**
     * Search properties
     */
    public function search($query, $filters = [], $tenantId = null) {
        $tenantId = $tenantId ?: $this->getCurrentTenantId();
        
        $whereConditions = ["tenant_id = ?", "deleted_at IS NULL"];
        $params = [$tenantId];
        
        // Text search
        if ($query) {
            $whereConditions[] = "(title LIKE ? OR description LIKE ? OR address LIKE ?)";
            $params[] = "%{$query}%";
            $params[] = "%{$query}%";
            $params[] = "%{$query}%";
        }
        
        // Filters
        if (isset($filters['type']) && $filters['type']) {
            $whereConditions[] = "type = ?";
            $params[] = $filters['type'];
        }
        
        if (isset($filters['status']) && $filters['status']) {
            $whereConditions[] = "status = ?";
            $params[] = $filters['status'];
        }
        
        if (isset($filters['purpose']) && $filters['purpose']) {
            $whereConditions[] = "purpose = ?";
            $params[] = $filters['purpose'];
        }
        
        if (isset($filters['city']) && $filters['city']) {
            $whereConditions[] = "city = ?";
            $params[] = $filters['city'];
        }
        
        if (isset($filters['min_price']) && $filters['min_price']) {
            $whereConditions[] = "price >= ?";
            $params[] = $filters['min_price'];
        }
        
        if (isset($filters['max_price']) && $filters['max_price']) {
            $whereConditions[] = "price <= ?";
            $params[] = $filters['max_price'];
        }
        
        if (isset($filters['bedrooms']) && $filters['bedrooms']) {
            $whereConditions[] = "bedrooms >= ?";
            $params[] = $filters['bedrooms'];
        }
        
        $sql = "SELECT * FROM {$this->table} 
                WHERE " . implode(' AND ', $whereConditions) . " 
                ORDER BY created_at DESC";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
    }
    
    /**
     * Get property statistics
     */
    public function getStats($tenantId = null) {
        $tenantId = $tenantId ?: $this->getCurrentTenantId();
        
        $stats = [];
        
        // Total properties
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE tenant_id = ? AND deleted_at IS NULL";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$tenantId]);
        $stats['total'] = $stmt->fetch()['count'];
        
        // Properties by status
        $sql = "SELECT status, COUNT(*) as count 
                FROM {$this->table} 
                WHERE tenant_id = ? AND deleted_at IS NULL 
                GROUP BY status";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$tenantId]);
        $stats['by_status'] = $stmt->fetchAll();
        
        // Properties by type
        $sql = "SELECT type, COUNT(*) as count 
                FROM {$this->table} 
                WHERE tenant_id = ? AND deleted_at IS NULL 
                GROUP BY type";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$tenantId]);
        $stats['by_type'] = $stmt->fetchAll();
        
        // Average price
        $sql = "SELECT AVG(price) as avg_price 
                FROM {$this->table} 
                WHERE tenant_id = ? AND deleted_at IS NULL";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$tenantId]);
        $stats['avg_price'] = $stmt->fetch()['avg_price'];
        
        // Total value
        $sql = "SELECT SUM(price) as total_value 
                FROM {$this->table} 
                WHERE tenant_id = ? AND deleted_at IS NULL";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$tenantId]);
        $stats['total_value'] = $stmt->fetch()['total_value'];
        
        return $stats;
    }
    
    /**
     * Get property images
     */
    public function getImages($propertyId) {
        $property = $this->find($propertyId);
        
        if (!$property || !$property['images']) {
            return [];
        }
        
        return json_decode($property['images'], true);
    }
    
    /**
     * Add image to property
     */
    public function addImage($propertyId, $imagePath) {
        $property = $this->find($propertyId);
        
        if (!$property) {
            return false;
        }
        
        $images = $property['images'] ? json_decode($property['images'], true) : [];
        $images[] = $imagePath;
        
        $sql = "UPDATE {$this->table} SET images = ?, updated_at = NOW() WHERE id = ?";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([json_encode($images), $propertyId]);
    }
    
    /**
     * Remove image from property
     */
    public function removeImage($propertyId, $imagePath) {
        $property = $this->find($propertyId);
        
        if (!$property || !$property['images']) {
            return false;
        }
        
        $images = json_decode($property['images'], true);
        $images = array_filter($images, function($img) use ($imagePath) {
            return $img !== $imagePath;
        });
        
        $sql = "UPDATE {$this->table} SET images = ?, updated_at = NOW() WHERE id = ?";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([json_encode(array_values($images)), $propertyId]);
    }
    
    /**
     * Increment views count
     */
    public function incrementViews($propertyId) {
        $sql = "UPDATE {$this->table} SET views_count = views_count + 1 WHERE id = ?";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([$propertyId]);
    }
    
    /**
     * Get similar properties
     */
    public function getSimilar($propertyId, $limit = 5) {
        $property = $this->find($propertyId);
        
        if (!$property) {
            return [];
        }
        
        $sql = "SELECT * FROM {$this->table} 
                WHERE id != ? 
                AND tenant_id = ? 
                AND type = ? 
                AND city = ? 
                AND price BETWEEN ? AND ? 
                AND deleted_at IS NULL 
                ORDER BY created_at DESC 
                LIMIT ?";
        
        $priceRange = $property['price'] * 0.2; // 20% price range
        $minPrice = $property['price'] - $priceRange;
        $maxPrice = $property['price'] + $priceRange;
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([
            $propertyId,
            $property['tenant_id'],
            $property['type'],
            $property['city'],
            $minPrice,
            $maxPrice,
            $limit
        ]);
        
        return $stmt->fetchAll();
    }
    
    /**
     * Get properties with pagination and filters
     */
    public function getPaginated($page = 1, $perPage = 20, $filters = [], $tenantId = null) {
        $tenantId = $tenantId ?: $this->getCurrentTenantId();
        $offset = ($page - 1) * $perPage;
        
        $whereConditions = ["tenant_id = ?", "deleted_at IS NULL"];
        $params = [$tenantId];
        
        // Apply filters
        if (isset($filters['search']) && $filters['search']) {
            $whereConditions[] = "(title LIKE ? OR description LIKE ? OR address LIKE ?)";
            $params[] = "%{$filters['search']}%";
            $params[] = "%{$filters['search']}%";
            $params[] = "%{$filters['search']}%";
        }
        
        if (isset($filters['type']) && $filters['type']) {
            $whereConditions[] = "type = ?";
            $params[] = $filters['type'];
        }
        
        if (isset($filters['status']) && $filters['status']) {
            $whereConditions[] = "status = ?";
            $params[] = $filters['status'];
        }
        
        $whereClause = implode(' AND ', $whereConditions);
        
        // Get total count
        $countSql = "SELECT COUNT(*) as total FROM {$this->table} WHERE {$whereClause}";
        $stmt = $this->db->prepare($countSql);
        $stmt->execute($params);
        $total = $stmt->fetch()['total'];
        
        // Get data
        $sql = "SELECT * FROM {$this->table} 
                WHERE {$whereClause} 
                ORDER BY created_at DESC 
                LIMIT {$offset}, {$perPage}";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        $data = $stmt->fetchAll();
        
        return [
            'data' => $data,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total' => $total,
                'total_pages' => ceil($total / $perPage),
                'has_prev' => $page > 1,
                'has_next' => $page < ceil($total / $perPage)
            ]
        ];
    }
    
    /**
     * Get property with owner and creator information
     */
    public function getWithDetails($propertyId) {
        $sql = "SELECT p.*, 
                       u1.first_name as created_by_first_name, 
                       u1.last_name as created_by_last_name,
                       u2.first_name as updated_by_first_name, 
                       u2.last_name as updated_by_last_name
                FROM {$this->table} p
                LEFT JOIN users u1 ON p.created_by = u1.id
                LEFT JOIN users u2 ON p.updated_by = u2.id
                WHERE p.id = ? AND p.deleted_at IS NULL";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$propertyId]);
        return $stmt->fetch();
    }
    
    /**
     * Get cities list for current tenant
     */
    public function getCities($tenantId = null) {
        $tenantId = $tenantId ?: $this->getCurrentTenantId();
        
        $sql = "SELECT DISTINCT city FROM {$this->table} 
                WHERE tenant_id = ? AND city IS NOT NULL AND deleted_at IS NULL 
                ORDER BY city";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$tenantId]);
        return $stmt->fetchAll(PDO::FETCH_COLUMN);
    }
}
