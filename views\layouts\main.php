<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="<?= $this->get('description', 'نظام إدارة المكاتب العقارية المتكامل') ?>">
    <meta name="keywords" content="<?= $this->get('keywords', 'عقارات، إدارة، مكاتب، نظام، سعودية') ?>">
    <meta name="author" content="Real Estate SaaS">
    
    <!-- CSRF Token -->
    <?= CSRF::metaTag() ?>
    
    <title><?= $this->get('title', APP_NAME) ?></title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?= $this->asset('images/favicon.ico') ?>">
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Arabic Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="<?= $this->asset('css/app.css') ?>" rel="stylesheet">
    
    <!-- Additional CSS -->
    <?php if (isset($additionalCSS)): ?>
        <?php foreach ($additionalCSS as $css): ?>
            <link href="<?= $css ?>" rel="stylesheet">
        <?php endforeach; ?>
    <?php endif; ?>
</head>
<body class="<?= $this->get('bodyClass', '') ?>">
    
    <!-- Navigation -->
    <?php $this->include('partials/navbar') ?>
    
    <!-- Sidebar -->
    <?php $this->include('partials/sidebar') ?>
    
    <!-- Main Content -->
    <main class="main-content">
        <div class="container-fluid">
            
            <!-- Flash Messages -->
            <?php $this->include('partials/flash-messages') ?>
            
            <!-- Page Header -->
            <?php if ($this->get('showPageHeader', true)): ?>
                <div class="page-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h1 class="page-title"><?= $this->get('pageTitle', $this->get('title')) ?></h1>
                            <?php if ($this->get('breadcrumbs')): ?>
                                <nav aria-label="breadcrumb">
                                    <ol class="breadcrumb">
                                        <?php foreach ($this->get('breadcrumbs') as $breadcrumb): ?>
                                            <?php if (isset($breadcrumb['url'])): ?>
                                                <li class="breadcrumb-item">
                                                    <a href="<?= $breadcrumb['url'] ?>"><?= $breadcrumb['title'] ?></a>
                                                </li>
                                            <?php else: ?>
                                                <li class="breadcrumb-item active"><?= $breadcrumb['title'] ?></li>
                                            <?php endif; ?>
                                        <?php endforeach; ?>
                                    </ol>
                                </nav>
                            <?php endif; ?>
                        </div>
                        <?php if ($this->get('pageActions')): ?>
                            <div class="col-auto">
                                <div class="page-actions">
                                    <?= $this->get('pageActions') ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- Page Content -->
            <div class="page-content">
                <?= $content ?>
            </div>
            
        </div>
    </main>
    
    <!-- Footer -->
    <?php $this->include('partials/footer') ?>
    
    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay d-none">
        <div class="loading-spinner">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <div class="loading-text">جاري التحميل...</div>
        </div>
    </div>
    
    <!-- Confirmation Modal -->
    <div class="modal fade" id="confirmModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تأكيد العملية</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p id="confirmMessage">هل أنت متأكد من هذا الإجراء؟</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-danger" id="confirmButton">تأكيد</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Scripts -->
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom JS -->
    <script src="<?= $this->asset('js/app.js') ?>"></script>
    
    <!-- Additional JS -->
    <?php if (isset($additionalJS)): ?>
        <?php foreach ($additionalJS as $js): ?>
            <script src="<?= $js ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>
    
    <!-- Inline Scripts -->
    <?php if ($this->get('inlineScripts')): ?>
        <script>
            <?= $this->get('inlineScripts') ?>
        </script>
    <?php endif; ?>
    
    <script>
        // Global JavaScript variables
        window.APP_URL = '<?= APP_URL ?>';
        window.CSRF_TOKEN = '<?= CSRF::token() ?>';
        window.USER_ROLE = '<?= Auth::user()['role'] ?? '' ?>';
        window.TENANT_ID = '<?= Auth::tenantId() ?? '' ?>';
        
        // Set CSRF token for all AJAX requests
        $.ajaxSetup({
            headers: {
                'X-CSRF-Token': window.CSRF_TOKEN
            }
        });
        
        // Initialize tooltips
        $(document).ready(function() {
            $('[data-bs-toggle="tooltip"]').tooltip();
        });
    </script>
    
</body>
</html>
