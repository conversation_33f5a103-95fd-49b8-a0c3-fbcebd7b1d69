<?php
/**
 * Session Management Class
 * Handles session operations, flash messages, and old input
 */

class Session {
    
    /**
     * Start session if not already started
     */
    public static function start() {
        if (session_status() === PHP_SESSION_NONE) {
            // Configure session settings
            ini_set('session.cookie_lifetime', SESSION_LIFETIME);
            ini_set('session.cookie_httponly', 1);
            ini_set('session.cookie_secure', 1);
            ini_set('session.use_strict_mode', 1);
            
            session_start();
            
            // Regenerate session ID periodically for security
            if (!isset($_SESSION['last_regeneration'])) {
                self::regenerate();
            } elseif (time() - $_SESSION['last_regeneration'] > 300) { // 5 minutes
                self::regenerate();
            }
        }
    }
    
    /**
     * Set session value
     */
    public static function set($key, $value) {
        self::start();
        $_SESSION[$key] = $value;
    }
    
    /**
     * Get session value
     */
    public static function get($key, $default = null) {
        self::start();
        return $_SESSION[$key] ?? $default;
    }
    
    /**
     * Check if session key exists
     */
    public static function has($key) {
        self::start();
        return isset($_SESSION[$key]);
    }
    
    /**
     * Remove session value
     */
    public static function remove($key) {
        self::start();
        if (isset($_SESSION[$key])) {
            unset($_SESSION[$key]);
        }
    }
    
    /**
     * Clear all session data
     */
    public static function clear() {
        self::start();
        $_SESSION = [];
    }
    
    /**
     * Destroy session
     */
    public static function destroy() {
        self::start();
        
        // Clear session data
        $_SESSION = [];
        
        // Delete session cookie
        if (ini_get("session.use_cookies")) {
            $params = session_get_cookie_params();
            setcookie(session_name(), '', time() - 42000,
                $params["path"], $params["domain"],
                $params["secure"], $params["httponly"]
            );
        }
        
        // Destroy session
        session_destroy();
    }
    
    /**
     * Regenerate session ID
     */
    public static function regenerate() {
        self::start();
        session_regenerate_id(true);
        $_SESSION['last_regeneration'] = time();
    }
    
    /**
     * Set flash message
     */
    public static function setFlash($type, $message) {
        self::start();
        $_SESSION['flash'][$type] = $message;
    }
    
    /**
     * Get flash message
     */
    public static function getFlash($type) {
        self::start();
        if (isset($_SESSION['flash'][$type])) {
            $message = $_SESSION['flash'][$type];
            unset($_SESSION['flash'][$type]);
            return $message;
        }
        return null;
    }
    
    /**
     * Check if flash message exists
     */
    public static function hasFlash($type) {
        self::start();
        return isset($_SESSION['flash'][$type]);
    }
    
    /**
     * Get all flash messages
     */
    public static function getAllFlash() {
        self::start();
        $flash = $_SESSION['flash'] ?? [];
        unset($_SESSION['flash']);
        return $flash;
    }
    
    /**
     * Set old input data
     */
    public static function setOldInput($data) {
        self::start();
        $_SESSION['old_input'] = $data;
    }
    
    /**
     * Get old input value
     */
    public static function getOldInput($key = null, $default = '') {
        self::start();
        if ($key === null) {
            $oldInput = $_SESSION['old_input'] ?? [];
            unset($_SESSION['old_input']);
            return $oldInput;
        }
        
        return $_SESSION['old_input'][$key] ?? $default;
    }
    
    /**
     * Check if old input exists
     */
    public static function hasOldInput($key = null) {
        self::start();
        if ($key === null) {
            return isset($_SESSION['old_input']);
        }
        return isset($_SESSION['old_input'][$key]);
    }
    
    /**
     * Clear old input
     */
    public static function clearOldInput() {
        self::start();
        unset($_SESSION['old_input']);
    }
    
    /**
     * Set validation errors
     */
    public static function setErrors($errors) {
        self::start();
        $_SESSION['errors'] = $errors;
    }
    
    /**
     * Get validation error
     */
    public static function getError($key) {
        self::start();
        return $_SESSION['errors'][$key] ?? null;
    }
    
    /**
     * Get all validation errors
     */
    public static function getErrors() {
        self::start();
        $errors = $_SESSION['errors'] ?? [];
        unset($_SESSION['errors']);
        return $errors;
    }
    
    /**
     * Check if validation error exists
     */
    public static function hasError($key) {
        self::start();
        return isset($_SESSION['errors'][$key]);
    }
    
    /**
     * Check if any validation errors exist
     */
    public static function hasErrors() {
        self::start();
        return isset($_SESSION['errors']) && !empty($_SESSION['errors']);
    }
    
    /**
     * Clear validation errors
     */
    public static function clearErrors() {
        self::start();
        unset($_SESSION['errors']);
    }
    
    /**
     * Set CSRF token
     */
    public static function setCSRFToken($token) {
        self::start();
        $_SESSION['csrf_token'] = $token;
    }
    
    /**
     * Get CSRF token
     */
    public static function getCSRFToken() {
        self::start();
        return $_SESSION['csrf_token'] ?? null;
    }
    
    /**
     * Verify CSRF token
     */
    public static function verifyCSRFToken($token) {
        $sessionToken = self::getCSRFToken();
        return $sessionToken && hash_equals($sessionToken, $token);
    }
    
    /**
     * Get session ID
     */
    public static function getId() {
        self::start();
        return session_id();
    }
    
    /**
     * Get session name
     */
    public static function getName() {
        return session_name();
    }
    
    /**
     * Check if session is active
     */
    public static function isActive() {
        return session_status() === PHP_SESSION_ACTIVE;
    }
    
    /**
     * Get session save path
     */
    public static function getSavePath() {
        return session_save_path();
    }
    
    /**
     * Set session save handler
     */
    public static function setSaveHandler($handler) {
        session_set_save_handler($handler);
    }
    
    /**
     * Get all session data (for debugging)
     */
    public static function all() {
        self::start();
        return $_SESSION;
    }
    
    /**
     * Flash input data for next request
     */
    public static function flashInput($input = null) {
        $input = $input ?: $_POST;
        
        // Remove sensitive fields
        $sensitiveFields = ['password', 'password_confirmation', '_token', '_method'];
        foreach ($sensitiveFields as $field) {
            unset($input[$field]);
        }
        
        self::setOldInput($input);
    }
    
    /**
     * Store intended URL for redirect after login
     */
    public static function setIntendedUrl($url) {
        self::set('intended_url', $url);
    }
    
    /**
     * Get intended URL and clear it
     */
    public static function getIntendedUrl($default = '/dashboard') {
        $url = self::get('intended_url', $default);
        self::remove('intended_url');
        return $url;
    }
    
    /**
     * Set user preferences
     */
    public static function setPreference($key, $value) {
        self::start();
        $_SESSION['preferences'][$key] = $value;
    }
    
    /**
     * Get user preference
     */
    public static function getPreference($key, $default = null) {
        self::start();
        return $_SESSION['preferences'][$key] ?? $default;
    }
    
    /**
     * Get all user preferences
     */
    public static function getPreferences() {
        self::start();
        return $_SESSION['preferences'] ?? [];
    }
}
