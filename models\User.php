<?php
/**
 * User Model
 * Handles user data and authentication
 */

class User extends Model {
    protected $table = 'users';
    protected $fillable = [
        'tenant_id', 'first_name', 'last_name', 'email', 'phone', 
        'password', 'role', 'avatar', 'status', 'settings'
    ];
    protected $hidden = ['password', 'remember_token'];
    protected $timestamps = true;
    protected $softDeletes = true;
    
    /**
     * Find user by email
     */
    public function findByEmail($email) {
        $sql = "SELECT * FROM {$this->table} WHERE email = ? AND deleted_at IS NULL";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$email]);
        return $stmt->fetch();
    }
    
    /**
     * Find user by remember token
     */
    public function findByRememberToken($token) {
        $sql = "SELECT * FROM {$this->table} WHERE remember_token = ? AND deleted_at IS NULL";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$token]);
        return $stmt->fetch();
    }
    
    /**
     * Update last login time
     */
    public function updateLastLogin($userId) {
        $sql = "UPDATE {$this->table} SET last_login_at = NOW() WHERE id = ?";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([$userId]);
    }
    
    /**
     * Set remember token
     */
    public function setRememberToken($userId, $token) {
        $sql = "UPDATE {$this->table} SET remember_token = ? WHERE id = ?";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([$token, $userId]);
    }
    
    /**
     * Clear remember token
     */
    public function clearRememberToken($userId) {
        $sql = "UPDATE {$this->table} SET remember_token = NULL WHERE id = ?";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([$userId]);
    }
    
    /**
     * Get users by tenant
     */
    public function getByTenant($tenantId) {
        $sql = "SELECT * FROM {$this->table} WHERE tenant_id = ? AND deleted_at IS NULL ORDER BY created_at DESC";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$tenantId]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get users by role
     */
    public function getByRole($role, $tenantId = null) {
        $sql = "SELECT * FROM {$this->table} WHERE role = ?";
        $params = [$role];
        
        if ($tenantId) {
            $sql .= " AND tenant_id = ?";
            $params[] = $tenantId;
        }
        
        $sql .= " AND deleted_at IS NULL ORDER BY created_at DESC";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
    }
    
    /**
     * Check if email exists
     */
    public function emailExists($email, $excludeId = null) {
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE email = ? AND deleted_at IS NULL";
        $params = [$email];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        $result = $stmt->fetch();
        
        return $result['count'] > 0;
    }
    
    /**
     * Get user with tenant information
     */
    public function getWithTenant($userId) {
        $sql = "SELECT u.*, t.name as tenant_name, t.slug as tenant_slug 
                FROM {$this->table} u 
                LEFT JOIN tenants t ON u.tenant_id = t.id 
                WHERE u.id = ? AND u.deleted_at IS NULL";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$userId]);
        return $stmt->fetch();
    }
    
    /**
     * Get user permissions
     */
    public function getPermissions($userId) {
        $sql = "SELECT p.name 
                FROM permissions p 
                INNER JOIN user_permissions up ON p.id = up.permission_id 
                WHERE up.user_id = ?";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$userId]);
        return $stmt->fetchAll(PDO::FETCH_COLUMN);
    }
    
    /**
     * Assign permission to user
     */
    public function assignPermission($userId, $permissionId, $grantedBy = null) {
        $sql = "INSERT INTO user_permissions (user_id, permission_id, granted_by) VALUES (?, ?, ?)
                ON DUPLICATE KEY UPDATE granted_by = VALUES(granted_by)";
        
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([$userId, $permissionId, $grantedBy]);
    }
    
    /**
     * Remove permission from user
     */
    public function removePermission($userId, $permissionId) {
        $sql = "DELETE FROM user_permissions WHERE user_id = ? AND permission_id = ?";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([$userId, $permissionId]);
    }
    
    /**
     * Get user statistics
     */
    public function getStats($tenantId = null) {
        $whereClause = $tenantId ? "WHERE tenant_id = ? AND deleted_at IS NULL" : "WHERE deleted_at IS NULL";
        $params = $tenantId ? [$tenantId] : [];
        
        $sql = "SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active,
                    SUM(CASE WHEN status = 'inactive' THEN 1 ELSE 0 END) as inactive,
                    SUM(CASE WHEN role = 'office_owner' THEN 1 ELSE 0 END) as owners,
                    SUM(CASE WHEN role = 'employee' THEN 1 ELSE 0 END) as employees,
                    SUM(CASE WHEN last_login_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as active_last_month
                FROM {$this->table} {$whereClause}";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetch();
    }
    
    /**
     * Search users
     */
    public function search($query, $tenantId = null) {
        $whereClause = "WHERE (first_name LIKE ? OR last_name LIKE ? OR email LIKE ? OR phone LIKE ?) AND deleted_at IS NULL";
        $params = ["%{$query}%", "%{$query}%", "%{$query}%", "%{$query}%"];
        
        if ($tenantId) {
            $whereClause .= " AND tenant_id = ?";
            $params[] = $tenantId;
        }
        
        $sql = "SELECT * FROM {$this->table} {$whereClause} ORDER BY created_at DESC";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
    }
    
    /**
     * Get full name
     */
    public function getFullName($user) {
        return trim($user['first_name'] . ' ' . $user['last_name']);
    }
    
    /**
     * Get user avatar URL
     */
    public function getAvatarUrl($user) {
        if ($user['avatar']) {
            return url('uploads/' . $user['avatar']);
        }
        
        // Generate default avatar based on initials
        $initials = mb_substr($user['first_name'], 0, 1) . mb_substr($user['last_name'], 0, 1);
        return "https://ui-avatars.com/api/?name=" . urlencode($initials) . "&background=007bff&color=fff&size=128";
    }
    
    /**
     * Check if user can access tenant
     */
    public function canAccessTenant($userId, $tenantId) {
        $user = $this->find($userId);
        
        if (!$user) {
            return false;
        }
        
        // Super admin can access all tenants
        if ($user['role'] === 'super_admin') {
            return true;
        }
        
        // Check if user belongs to the tenant
        return $user['tenant_id'] == $tenantId;
    }
    
    /**
     * Get recent activity
     */
    public function getRecentActivity($userId, $limit = 10) {
        $sql = "SELECT * FROM activity_logs 
                WHERE user_id = ? 
                ORDER BY created_at DESC 
                LIMIT ?";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$userId, $limit]);
        return $stmt->fetchAll();
    }
    
    /**
     * Update user settings
     */
    public function updateSettings($userId, $settings) {
        $sql = "UPDATE {$this->table} SET settings = ? WHERE id = ?";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([json_encode($settings), $userId]);
    }
    
    /**
     * Get user settings
     */
    public function getSettings($userId) {
        $sql = "SELECT settings FROM {$this->table} WHERE id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$userId]);
        $result = $stmt->fetch();
        
        return $result ? json_decode($result['settings'], true) : [];
    }
}
