<?php
/**
 * Invoice Model
 * Handles invoice data and operations
 */

class Invoice extends Model {
    protected $table = 'invoices';
    protected $fillable = [
        'tenant_id', 'invoice_number', 'client_id', 'property_id', 'type', 'status',
        'issue_date', 'due_date', 'paid_date', 'subtotal', 'tax_rate', 'tax_amount',
        'discount_amount', 'total_amount', 'paid_amount', 'currency', 'payment_method',
        'payment_reference', 'notes', 'items', 'created_by', 'updated_by'
    ];
    protected $timestamps = true;
    protected $softDeletes = true;
    
    /**
     * Generate unique invoice number
     */
    public function generateInvoiceNumber($tenantId = null) {
        $tenantId = $tenantId ?: $this->getCurrentTenantId();
        
        // Get current year and month
        $year = date('Y');
        $month = date('m');
        
        // Get last invoice number for this tenant, year, and month
        $sql = "SELECT invoice_number FROM {$this->table} 
                WHERE tenant_id = ? 
                AND invoice_number LIKE ? 
                ORDER BY id DESC 
                LIMIT 1";
        
        $prefix = "INV-{$year}{$month}-";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$tenantId, $prefix . '%']);
        $lastInvoice = $stmt->fetch();
        
        if ($lastInvoice) {
            // Extract number and increment
            $lastNumber = (int)substr($lastInvoice['invoice_number'], -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }
        
        return $prefix . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }
    
    /**
     * Get invoices by status
     */
    public function getByStatus($status, $tenantId = null) {
        $tenantId = $tenantId ?: $this->getCurrentTenantId();
        
        $sql = "SELECT i.*, c.first_name, c.last_name, p.title as property_title
                FROM {$this->table} i
                LEFT JOIN clients c ON i.client_id = c.id
                LEFT JOIN properties p ON i.property_id = p.id
                WHERE i.status = ? AND i.tenant_id = ? AND i.deleted_at IS NULL 
                ORDER BY i.created_at DESC";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$status, $tenantId]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get overdue invoices
     */
    public function getOverdue($tenantId = null) {
        $tenantId = $tenantId ?: $this->getCurrentTenantId();
        
        $sql = "SELECT i.*, c.first_name, c.last_name, p.title as property_title
                FROM {$this->table} i
                LEFT JOIN clients c ON i.client_id = c.id
                LEFT JOIN properties p ON i.property_id = p.id
                WHERE i.status IN ('مرسلة') 
                AND i.due_date < CURDATE() 
                AND i.tenant_id = ? 
                AND i.deleted_at IS NULL 
                ORDER BY i.due_date ASC";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$tenantId]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get invoice statistics
     */
    public function getStats($tenantId = null) {
        $tenantId = $tenantId ?: $this->getCurrentTenantId();
        
        $stats = [];
        
        // Total invoices
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE tenant_id = ? AND deleted_at IS NULL";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$tenantId]);
        $stats['total'] = $stmt->fetch()['count'];
        
        // Invoices by status
        $sql = "SELECT status, COUNT(*) as count, SUM(total_amount) as total_amount
                FROM {$this->table} 
                WHERE tenant_id = ? AND deleted_at IS NULL 
                GROUP BY status";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$tenantId]);
        $stats['by_status'] = $stmt->fetchAll();
        
        // Total revenue
        $sql = "SELECT SUM(total_amount) as total_revenue 
                FROM {$this->table} 
                WHERE tenant_id = ? AND status = 'مدفوعة' AND deleted_at IS NULL";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$tenantId]);
        $stats['total_revenue'] = $stmt->fetch()['total_revenue'] ?: 0;
        
        // Pending amount
        $sql = "SELECT SUM(total_amount - paid_amount) as pending_amount 
                FROM {$this->table} 
                WHERE tenant_id = ? AND status IN ('مرسلة', 'متأخرة') AND deleted_at IS NULL";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$tenantId]);
        $stats['pending_amount'] = $stmt->fetch()['pending_amount'] ?: 0;
        
        // This month revenue
        $sql = "SELECT SUM(total_amount) as month_revenue 
                FROM {$this->table} 
                WHERE tenant_id = ? 
                AND status = 'مدفوعة' 
                AND MONTH(paid_date) = MONTH(CURRENT_DATE()) 
                AND YEAR(paid_date) = YEAR(CURRENT_DATE())
                AND deleted_at IS NULL";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$tenantId]);
        $stats['month_revenue'] = $stmt->fetch()['month_revenue'] ?: 0;
        
        return $stats;
    }
    
    /**
     * Calculate invoice totals
     */
    public function calculateTotals($items, $taxRate = null, $discountAmount = 0) {
        $taxRate = $taxRate ?: DEFAULT_TAX_RATE;
        
        $subtotal = 0;
        
        foreach ($items as $item) {
            $itemTotal = $item['quantity'] * $item['price'];
            $subtotal += $itemTotal;
        }
        
        $taxAmount = ($subtotal - $discountAmount) * ($taxRate / 100);
        $totalAmount = $subtotal - $discountAmount + $taxAmount;
        
        return [
            'subtotal' => $subtotal,
            'tax_amount' => $taxAmount,
            'discount_amount' => $discountAmount,
            'total_amount' => $totalAmount
        ];
    }
    
    /**
     * Mark invoice as paid
     */
    public function markAsPaid($invoiceId, $paymentMethod = null, $paymentReference = null) {
        $sql = "UPDATE {$this->table} 
                SET status = 'مدفوعة', 
                    paid_date = CURDATE(), 
                    paid_amount = total_amount,
                    payment_method = ?,
                    payment_reference = ?,
                    updated_at = NOW()
                WHERE id = ?";
        
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([$paymentMethod, $paymentReference, $invoiceId]);
    }
    
    /**
     * Record partial payment
     */
    public function recordPayment($invoiceId, $amount, $paymentMethod = null, $paymentReference = null) {
        $invoice = $this->find($invoiceId);
        
        if (!$invoice) {
            return false;
        }
        
        $newPaidAmount = $invoice['paid_amount'] + $amount;
        $status = $invoice['status'];
        
        // Update status if fully paid
        if ($newPaidAmount >= $invoice['total_amount']) {
            $status = 'مدفوعة';
            $newPaidAmount = $invoice['total_amount'];
        }
        
        $sql = "UPDATE {$this->table} 
                SET paid_amount = ?, 
                    status = ?,
                    payment_method = ?,
                    payment_reference = ?,
                    updated_at = NOW()
                WHERE id = ?";
        
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([$newPaidAmount, $status, $paymentMethod, $paymentReference, $invoiceId]);
    }
    
    /**
     * Get invoice with related data
     */
    public function getWithDetails($invoiceId) {
        $sql = "SELECT i.*, 
                       c.first_name as client_first_name, 
                       c.last_name as client_last_name,
                       c.email as client_email,
                       c.phone as client_phone,
                       c.address as client_address,
                       p.title as property_title,
                       p.address as property_address,
                       t.name as tenant_name,
                       t.email as tenant_email,
                       t.phone as tenant_phone,
                       t.address as tenant_address,
                       t.tax_number as tenant_tax_number,
                       u1.first_name as created_by_first_name,
                       u1.last_name as created_by_last_name
                FROM {$this->table} i
                LEFT JOIN clients c ON i.client_id = c.id
                LEFT JOIN properties p ON i.property_id = p.id
                LEFT JOIN tenants t ON i.tenant_id = t.id
                LEFT JOIN users u1 ON i.created_by = u1.id
                WHERE i.id = ? AND i.deleted_at IS NULL";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$invoiceId]);
        return $stmt->fetch();
    }
    
    /**
     * Search invoices
     */
    public function search($query, $filters = [], $tenantId = null) {
        $tenantId = $tenantId ?: $this->getCurrentTenantId();
        
        $whereConditions = ["i.tenant_id = ?", "i.deleted_at IS NULL"];
        $params = [$tenantId];
        
        // Text search
        if ($query) {
            $whereConditions[] = "(i.invoice_number LIKE ? OR c.first_name LIKE ? OR c.last_name LIKE ? OR p.title LIKE ?)";
            $params[] = "%{$query}%";
            $params[] = "%{$query}%";
            $params[] = "%{$query}%";
            $params[] = "%{$query}%";
        }
        
        // Filters
        if (isset($filters['status']) && $filters['status']) {
            $whereConditions[] = "i.status = ?";
            $params[] = $filters['status'];
        }
        
        if (isset($filters['client_id']) && $filters['client_id']) {
            $whereConditions[] = "i.client_id = ?";
            $params[] = $filters['client_id'];
        }
        
        if (isset($filters['date_from']) && $filters['date_from']) {
            $whereConditions[] = "i.issue_date >= ?";
            $params[] = $filters['date_from'];
        }
        
        if (isset($filters['date_to']) && $filters['date_to']) {
            $whereConditions[] = "i.issue_date <= ?";
            $params[] = $filters['date_to'];
        }
        
        $sql = "SELECT i.*, c.first_name, c.last_name, p.title as property_title
                FROM {$this->table} i
                LEFT JOIN clients c ON i.client_id = c.id
                LEFT JOIN properties p ON i.property_id = p.id
                WHERE " . implode(' AND ', $whereConditions) . " 
                ORDER BY i.created_at DESC";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
    }
    
    /**
     * Get monthly revenue data
     */
    public function getMonthlyRevenue($tenantId = null, $months = 12) {
        $tenantId = $tenantId ?: $this->getCurrentTenantId();
        
        $sql = "SELECT 
                    DATE_FORMAT(paid_date, '%Y-%m') as month,
                    SUM(total_amount) as revenue,
                    COUNT(*) as count
                FROM {$this->table} 
                WHERE tenant_id = ? 
                AND status = 'مدفوعة' 
                AND paid_date >= DATE_SUB(CURRENT_DATE(), INTERVAL ? MONTH)
                AND deleted_at IS NULL
                GROUP BY DATE_FORMAT(paid_date, '%Y-%m')
                ORDER BY month";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$tenantId, $months]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get invoices with pagination
     */
    public function getPaginated($page = 1, $perPage = 20, $filters = [], $tenantId = null) {
        $tenantId = $tenantId ?: $this->getCurrentTenantId();
        $offset = ($page - 1) * $perPage;
        
        $whereConditions = ["i.tenant_id = ?", "i.deleted_at IS NULL"];
        $params = [$tenantId];
        
        // Apply filters
        if (isset($filters['search']) && $filters['search']) {
            $whereConditions[] = "(i.invoice_number LIKE ? OR c.first_name LIKE ? OR c.last_name LIKE ?)";
            $params[] = "%{$filters['search']}%";
            $params[] = "%{$filters['search']}%";
            $params[] = "%{$filters['search']}%";
        }
        
        if (isset($filters['status']) && $filters['status']) {
            $whereConditions[] = "i.status = ?";
            $params[] = $filters['status'];
        }
        
        $whereClause = implode(' AND ', $whereConditions);
        
        // Get total count
        $countSql = "SELECT COUNT(*) as total 
                     FROM {$this->table} i
                     LEFT JOIN clients c ON i.client_id = c.id
                     WHERE {$whereClause}";
        $stmt = $this->db->prepare($countSql);
        $stmt->execute($params);
        $total = $stmt->fetch()['total'];
        
        // Get data
        $sql = "SELECT i.*, c.first_name, c.last_name, p.title as property_title
                FROM {$this->table} i
                LEFT JOIN clients c ON i.client_id = c.id
                LEFT JOIN properties p ON i.property_id = p.id
                WHERE {$whereClause} 
                ORDER BY i.created_at DESC 
                LIMIT {$offset}, {$perPage}";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        $data = $stmt->fetchAll();
        
        return [
            'data' => $data,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total' => $total,
                'total_pages' => ceil($total / $perPage),
                'has_prev' => $page > 1,
                'has_next' => $page < ceil($total / $perPage)
            ]
        ];
    }
}
