<?php if ($this->hasFlash()): ?>
    <div class="flash-messages">
        <?php foreach ($this->flashMessage() as $type => $message): ?>
            <div class="alert alert-<?= $type === 'error' ? 'danger' : $type ?> alert-dismissible fade show" role="alert">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0 me-3">
                        <?php
                        $icons = [
                            'success' => 'fas fa-check-circle',
                            'error' => 'fas fa-exclamation-circle',
                            'warning' => 'fas fa-exclamation-triangle',
                            'info' => 'fas fa-info-circle'
                        ];
                        $icon = $icons[$type] ?? $icons['info'];
                        ?>
                        <i class="<?= $icon ?> fa-lg"></i>
                    </div>
                    <div class="flex-grow-1">
                        <?= $this->escape($message) ?>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="إغلاق"></button>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
<?php endif; ?>

<style>
.flash-messages {
    margin-bottom: 1.5rem;
}

.flash-messages .alert {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 1rem;
}

.flash-messages .alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    border-right: 4px solid #28a745;
}

.flash-messages .alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
    border-right: 4px solid #dc3545;
}

.flash-messages .alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
    border-right: 4px solid #ffc107;
}

.flash-messages .alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
    border-right: 4px solid #17a2b8;
}

.flash-messages .btn-close {
    opacity: 0.7;
}

.flash-messages .btn-close:hover {
    opacity: 1;
}
</style>
