<nav class="navbar navbar-expand-lg">
    <div class="container-fluid">
        
        <!-- Brand -->
        <a class="navbar-brand" href="/dashboard">
            <i class="fas fa-building me-2"></i>
            <?= APP_NAME ?>
        </a>
        
        <!-- Mobile Toggle -->
        <button class="navbar-toggler sidebar-toggle d-lg-none" type="button">
            <i class="fas fa-bars"></i>
        </button>
        
        <!-- Navbar Items -->
        <div class="navbar-nav me-auto">
            
            <!-- Notifications -->
            <div class="nav-item dropdown">
                <a class="nav-link dropdown-toggle position-relative" href="#" id="notificationsDropdown" role="button" data-bs-toggle="dropdown">
                    <i class="fas fa-bell"></i>
                    <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                        3
                        <span class="visually-hidden">إشعارات غير مقروءة</span>
                    </span>
                </a>
                <ul class="dropdown-menu dropdown-menu-end" style="width: 300px;">
                    <li class="dropdown-header">
                        <strong>الإشعارات</strong>
                        <span class="badge bg-primary rounded-pill float-start">3</span>
                    </li>
                    <li><hr class="dropdown-divider"></li>
                    
                    <!-- Notification Items -->
                    <li>
                        <a class="dropdown-item d-flex align-items-start" href="#">
                            <div class="flex-shrink-0 me-3">
                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                    <i class="fas fa-home text-white"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1">عقار جديد</h6>
                                <p class="mb-1 text-muted small">تم إضافة عقار جديد في الرياض</p>
                                <small class="text-muted">منذ 5 دقائق</small>
                            </div>
                        </a>
                    </li>
                    
                    <li>
                        <a class="dropdown-item d-flex align-items-start" href="#">
                            <div class="flex-shrink-0 me-3">
                                <div class="bg-success rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                    <i class="fas fa-user text-white"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1">عميل جديد</h6>
                                <p class="mb-1 text-muted small">تم تسجيل عميل جديد</p>
                                <small class="text-muted">منذ 15 دقيقة</small>
                            </div>
                        </a>
                    </li>
                    
                    <li>
                        <a class="dropdown-item d-flex align-items-start" href="#">
                            <div class="flex-shrink-0 me-3">
                                <div class="bg-warning rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                    <i class="fas fa-file-invoice text-white"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1">فاتورة متأخرة</h6>
                                <p class="mb-1 text-muted small">فاتورة رقم #1234 متأخرة الدفع</p>
                                <small class="text-muted">منذ ساعة</small>
                            </div>
                        </a>
                    </li>
                    
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <a class="dropdown-item text-center" href="/notifications">
                            عرض جميع الإشعارات
                        </a>
                    </li>
                </ul>
            </div>
            
            <!-- Quick Actions -->
            <div class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" id="quickActionsDropdown" role="button" data-bs-toggle="dropdown">
                    <i class="fas fa-plus-circle"></i>
                    إضافة سريعة
                </a>
                <ul class="dropdown-menu dropdown-menu-end">
                    <?php if (can('properties.create')): ?>
                        <li>
                            <a class="dropdown-item" href="/properties/create">
                                <i class="fas fa-home me-2"></i>
                                إضافة عقار
                            </a>
                        </li>
                    <?php endif; ?>
                    
                    <?php if (can('clients.create')): ?>
                        <li>
                            <a class="dropdown-item" href="/clients/create">
                                <i class="fas fa-user me-2"></i>
                                إضافة عميل
                            </a>
                        </li>
                    <?php endif; ?>
                    
                    <?php if (can('invoices.create')): ?>
                        <li>
                            <a class="dropdown-item" href="/invoices/create">
                                <i class="fas fa-file-invoice me-2"></i>
                                إنشاء فاتورة
                            </a>
                        </li>
                    <?php endif; ?>
                    
                    <?php if (can('users.create')): ?>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item" href="/users/create">
                                <i class="fas fa-user-plus me-2"></i>
                                إضافة مستخدم
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
        
        <!-- User Menu -->
        <div class="navbar-nav">
            <div class="nav-item dropdown">
                <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                    <img src="<?= (new User())->getAvatarUrl($user) ?>" 
                         alt="<?= e($user['first_name'] . ' ' . $user['last_name']) ?>" 
                         class="rounded-circle me-2" 
                         width="32" 
                         height="32">
                    <span class="d-none d-md-inline">
                        <?= e($user['first_name'] . ' ' . $user['last_name']) ?>
                    </span>
                </a>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li class="dropdown-header">
                        <div class="text-center">
                            <img src="<?= (new User())->getAvatarUrl($user) ?>" 
                                 alt="<?= e($user['first_name'] . ' ' . $user['last_name']) ?>" 
                                 class="rounded-circle mb-2" 
                                 width="60" 
                                 height="60">
                            <h6 class="mb-0"><?= e($user['first_name'] . ' ' . $user['last_name']) ?></h6>
                            <small class="text-muted"><?= e($user['email']) ?></small>
                            <div class="mt-1">
                                <?php
                                $roleNames = [
                                    'super_admin' => 'مدير النظام',
                                    'office_owner' => 'مالك المكتب',
                                    'employee' => 'موظف'
                                ];
                                ?>
                                <span class="badge bg-primary"><?= $roleNames[$user['role']] ?? $user['role'] ?></span>
                            </div>
                        </div>
                    </li>
                    <li><hr class="dropdown-divider"></li>
                    
                    <li>
                        <a class="dropdown-item" href="/profile">
                            <i class="fas fa-user me-2"></i>
                            الملف الشخصي
                        </a>
                    </li>
                    
                    <li>
                        <a class="dropdown-item" href="/settings">
                            <i class="fas fa-cog me-2"></i>
                            الإعدادات
                        </a>
                    </li>
                    
                    <?php if (hasRole('super_admin')): ?>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item" href="/admin">
                                <i class="fas fa-tools me-2"></i>
                                لوحة الإدارة
                            </a>
                        </li>
                    <?php endif; ?>
                    
                    <li><hr class="dropdown-divider"></li>
                    
                    <li>
                        <a class="dropdown-item" href="/help">
                            <i class="fas fa-question-circle me-2"></i>
                            المساعدة
                        </a>
                    </li>
                    
                    <li>
                        <form method="POST" action="/logout" class="d-inline">
                            <?= csrf_field() ?>
                            <button type="submit" class="dropdown-item text-danger">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                تسجيل الخروج
                            </button>
                        </form>
                    </li>
                </ul>
            </div>
        </div>
        
    </div>
</nav>
