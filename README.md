# نظام إدارة المكاتب العقارية (SaaS)
## Real Estate Office Management System

نظام متكامل لإدارة المكاتب العقارية مع دعم متعدد المستأجرين (Multi-tenant) باللغة العربية.

## المميزات الرئيسية

### 🏢 إدارة المكاتب العقارية
- نظام متعدد المستأجرين مع عزل كامل للبيانات
- إدارة المكاتب والفروع
- نظام اشتراكات مرن
- لوحة تحكم منفصلة لكل مكتب

### 🏠 إدارة العقارات
- إضافة وتعديل العقارات بتفاصيل شاملة
- رفع صور متعددة للعقارات
- تصنيف العقارات حسب النوع والغرض
- نظام بحث متقدم
- خرائط تفاعلية لمواقع العقارات
- تتبع حالة العقارات (متاح، مباع، مؤجر)

### 👥 إدارة العملاء
- قاعدة بيانات شاملة للعملاء
- تصنيف العملاء (مشتري، بائع، مستأجر، مؤجر)
- تتبع تفاعلات العملاء
- نظام مطابقة العملاء مع العقارات المناسبة
- سجل كامل لتاريخ التعاملات

### 💰 النظام المالي
- إنشاء وإدارة الفواتير
- تتبع المدفوعات والمستحقات
- حساب العمولات تلقائياً
- تقارير مالية شاملة
- دعم ضريبة القيمة المضافة

### 📊 التقارير والإحصائيات
- تقارير مبيعات تفصيلية
- إحصائيات الأداء
- تقارير العملاء والعقارات
- رسوم بيانية تفاعلية
- تصدير التقارير بصيغ متعددة

### 👤 إدارة المستخدمين
- نظام أدوار ومستويات صلاحيات
- إدارة الموظفين والمستخدمين
- تتبع نشاطات المستخدمين
- نظام تسجيل دخول آمن

### 🔒 الأمان والحماية
- تشفير كلمات المرور
- حماية من هجمات CSRF
- جلسات آمنة
- نظام صلاحيات متقدم
- سجل كامل للأنشطة

## متطلبات النظام

### الخادم
- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- Apache/Nginx مع mod_rewrite
- SSL Certificate (مُوصى به)

### المتصفح
- Chrome 70+
- Firefox 65+
- Safari 12+
- Edge 79+

## التثبيت

### 1. تحميل الملفات
```bash
git clone https://github.com/your-repo/real-estate-saas.git
cd real-estate-saas
```

### 2. إعداد قاعدة البيانات
```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE real_estate_saas CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- استيراد الهيكل
mysql -u username -p real_estate_saas < database/schema.sql
```

### 3. إعداد الملفات
```bash
# نسخ ملف الإعدادات
cp config/config.example.php config/config.php

# تعديل إعدادات قاعدة البيانات
nano config/config.php
```

### 4. إعداد الصلاحيات
```bash
# صلاحيات المجلدات
chmod 755 uploads/
chmod 755 logs/
chmod 644 config/config.php

# إنشاء المجلدات المطلوبة
mkdir -p uploads/properties
mkdir -p uploads/avatars
mkdir -p uploads/documents
mkdir -p logs
```

### 5. إعداد الخادم
```apache
# Apache Virtual Host
<VirtualHost *:80>
    ServerName your-domain.com
    DocumentRoot /path/to/real-estate-saas
    
    <Directory /path/to/real-estate-saas>
        AllowOverride All
        Require all granted
    </Directory>
</VirtualHost>
```

## الإعدادات

### ملف config/config.php
```php
// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'real_estate_saas');
define('DB_USER', 'username');
define('DB_PASS', 'password');

// إعدادات التطبيق
define('APP_NAME', 'نظام إدارة المكاتب العقارية');
define('APP_URL', 'https://your-domain.com');
define('APP_VERSION', '1.0.0');

// إعدادات الأمان
define('JWT_SECRET', 'your-secret-key');
define('CSRF_TOKEN_NAME', '_token');
```

## الاستخدام

### تسجيل الدخول الأول
1. انتقل إلى `/register` لإنشاء حساب جديد
2. أدخل بيانات المكتب العقاري
3. أدخل بياناتك الشخصية كمدير المكتب
4. ستحصل على فترة تجريبية مجانية لمدة شهر

### إضافة عقار جديد
1. انتقل إلى "العقارات" > "إضافة عقار"
2. أدخل تفاصيل العقار
3. ارفع الصور والمستندات
4. حدد الموقع على الخريطة
5. احفظ العقار

### إدارة العملاء
1. انتقل إلى "العملاء" > "إضافة عميل"
2. أدخل بيانات العميل
3. حدد نوع العميل ومتطلباته
4. احفظ البيانات
5. استخدم نظام المطابقة لإيجاد العقارات المناسبة

## API Documentation

النظام يوفر API RESTful للتكامل مع التطبيقات الأخرى:

### Authentication
```
POST /api/auth/login
POST /api/auth/logout
POST /api/auth/refresh
```

### Properties
```
GET    /api/properties
POST   /api/properties
GET    /api/properties/{id}
PUT    /api/properties/{id}
DELETE /api/properties/{id}
```

### Clients
```
GET    /api/clients
POST   /api/clients
GET    /api/clients/{id}
PUT    /api/clients/{id}
DELETE /api/clients/{id}
```

## الدعم والمساعدة

### الوثائق
- [دليل المستخدم](docs/user-guide.md)
- [دليل المطور](docs/developer-guide.md)
- [API Reference](docs/api-reference.md)

### الدعم الفني
- البريد الإلكتروني: <EMAIL>
- الهاتف: +966 50 123 4567
- الدردشة المباشرة: متوفرة في النظام

## المساهمة في التطوير

نرحب بمساهماتكم في تطوير النظام:

1. Fork المشروع
2. إنشاء branch جديد للميزة
3. Commit التغييرات
4. Push إلى Branch
5. إنشاء Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الإصدارات

### الإصدار 1.0.0 (الحالي)
- إدارة العقارات والعملاء
- النظام المالي والفواتير
- التقارير الأساسية
- نظام المستخدمين والصلاحيات

### الإصدارات القادمة
- تطبيق الهاتف المحمول
- نظام CRM متقدم
- تكامل مع منصات التواصل الاجتماعي
- نظام التسويق الإلكتروني

## الشكر والتقدير

شكر خاص لجميع المساهمين في تطوير هذا النظام:
- فريق التطوير
- فريق التصميم
- فريق الاختبار
- المكاتب العقارية التي ساعدت في الاختبار

---

© 2024 نظام إدارة المكاتب العقارية. جميع الحقوق محفوظة.
