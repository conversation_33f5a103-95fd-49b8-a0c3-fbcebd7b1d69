<?php
$stats = $dashboardData['stats'] ?? [];
$userRole = $userRole ?? 'employee';
?>

<!-- Welcome Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="welcome-card bg-gradient-primary text-white rounded-3 p-4">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h2 class="mb-2">
                        مرحباً، <?= e($user['first_name'] . ' ' . $user['last_name']) ?>
                    </h2>
                    <p class="mb-0 opacity-75">
                        <?php if ($userRole === 'super_admin'): ?>
                            مرحباً بك في لوحة تحكم النظام. يمكنك إدارة جميع المكاتب والمستخدمين من هنا.
                        <?php elseif ($userRole === 'office_owner'): ?>
                            مرحباً بك في لوحة تحكم مكتبك العقاري. تابع أداء مكتبك وإدارة عملياتك بكفاءة.
                        <?php else: ?>
                            مرحباً بك في نظام إدارة المكتب العقاري. ابدأ عملك اليوم بكفاءة وفعالية.
                        <?php endif; ?>
                    </p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="welcome-icon">
                        <i class="fas fa-chart-line fa-3x opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <?php if ($userRole === 'super_admin'): ?>
        <!-- Super Admin Stats -->
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="stat-card">
                <div class="stat-icon bg-primary text-white">
                    <i class="fas fa-building"></i>
                </div>
                <div class="stat-value"><?= arabicNumber($stats['total_tenants'] ?? 0) ?></div>
                <div class="stat-label">إجمالي المكاتب</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up me-1"></i>
                    +<?= arabicNumber($stats['active_tenants'] ?? 0) ?> نشط
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="stat-card">
                <div class="stat-icon bg-success text-white">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-value"><?= arabicNumber($stats['total_users'] ?? 0) ?></div>
                <div class="stat-label">إجمالي المستخدمين</div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="stat-card">
                <div class="stat-icon bg-info text-white">
                    <i class="fas fa-home"></i>
                </div>
                <div class="stat-value"><?= arabicNumber($stats['total_properties'] ?? 0) ?></div>
                <div class="stat-label">إجمالي العقارات</div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="stat-card">
                <div class="stat-icon bg-warning text-white">
                    <i class="fas fa-file-invoice"></i>
                </div>
                <div class="stat-value"><?= arabicNumber($stats['total_invoices'] ?? 0) ?></div>
                <div class="stat-label">إجمالي الفواتير</div>
            </div>
        </div>
        
    <?php else: ?>
        <!-- Tenant Stats -->
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="stat-card">
                <div class="stat-icon bg-primary text-white">
                    <i class="fas fa-home"></i>
                </div>
                <div class="stat-value"><?= arabicNumber($stats['total_properties'] ?? 0) ?></div>
                <div class="stat-label">إجمالي العقارات</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up me-1"></i>
                    +<?= arabicNumber($stats['available_properties'] ?? 0) ?> متاح
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="stat-card">
                <div class="stat-icon bg-success text-white">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-value"><?= arabicNumber($stats['total_clients'] ?? 0) ?></div>
                <div class="stat-label">إجمالي العملاء</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up me-1"></i>
                    +<?= arabicNumber($stats['active_clients'] ?? 0) ?> نشط
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="stat-card">
                <div class="stat-icon bg-info text-white">
                    <i class="fas fa-file-invoice"></i>
                </div>
                <div class="stat-value"><?= arabicNumber($stats['total_invoices'] ?? 0) ?></div>
                <div class="stat-label">إجمالي الفواتير</div>
                <?php if (($stats['pending_invoices'] ?? 0) > 0): ?>
                    <div class="stat-change negative">
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        <?= arabicNumber($stats['pending_invoices']) ?> معلقة
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="stat-card">
                <div class="stat-icon bg-warning text-white">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stat-value"><?= currency($dashboardData['financial']['revenue_this_month'] ?? 0) ?></div>
                <div class="stat-label">إيرادات هذا الشهر</div>
                <?php
                $thisMonth = $dashboardData['financial']['revenue_this_month'] ?? 0;
                $lastMonth = $dashboardData['financial']['revenue_last_month'] ?? 0;
                $change = $lastMonth > 0 ? (($thisMonth - $lastMonth) / $lastMonth) * 100 : 0;
                ?>
                <?php if ($change != 0): ?>
                    <div class="stat-change <?= $change > 0 ? 'positive' : 'negative' ?>">
                        <i class="fas fa-arrow-<?= $change > 0 ? 'up' : 'down' ?> me-1"></i>
                        <?= abs(round($change, 1)) ?>% عن الشهر الماضي
                    </div>
                <?php endif; ?>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Charts and Recent Activity -->
<div class="row">
    
    <!-- Charts Section -->
    <?php if ($userRole !== 'super_admin'): ?>
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        الإيرادات الشهرية
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="revenueChart" height="300"></canvas>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        العقارات حسب الحالة
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="propertiesChart" height="300"></canvas>
                </div>
            </div>
        </div>
    <?php endif; ?>
    
    <!-- Recent Activity -->
    <div class="col-lg-<?= $userRole === 'super_admin' ? '12' : '6' ?> mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-clock me-2"></i>
                    النشاطات الأخيرة
                </h5>
                <a href="/activity" class="btn btn-sm btn-outline-primary">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                <div class="activity-list">
                    <?php if (isset($dashboardData['recent_properties']) && !empty($dashboardData['recent_properties'])): ?>
                        <?php foreach (array_slice($dashboardData['recent_properties'], 0, 5) as $property): ?>
                            <div class="activity-item">
                                <div class="activity-icon bg-primary">
                                    <i class="fas fa-home"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">تم إضافة عقار جديد</div>
                                    <div class="activity-description"><?= e($property['title']) ?></div>
                                    <div class="activity-time"><?= timeAgo($property['created_at']) ?></div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-inbox fa-3x mb-3"></i>
                            <p>لا توجد نشاطات حديثة</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="col-lg-<?= $userRole === 'super_admin' ? '12' : '6' ?> mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <?php if (can('properties.create')): ?>
                        <div class="col-md-6">
                            <a href="/properties/create" class="btn btn-outline-primary w-100 py-3">
                                <i class="fas fa-home fa-2x d-block mb-2"></i>
                                إضافة عقار
                            </a>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (can('clients.create')): ?>
                        <div class="col-md-6">
                            <a href="/clients/create" class="btn btn-outline-success w-100 py-3">
                                <i class="fas fa-user-plus fa-2x d-block mb-2"></i>
                                إضافة عميل
                            </a>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (can('invoices.create')): ?>
                        <div class="col-md-6">
                            <a href="/invoices/create" class="btn btn-outline-warning w-100 py-3">
                                <i class="fas fa-file-invoice fa-2x d-block mb-2"></i>
                                إنشاء فاتورة
                            </a>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (can('reports.view')): ?>
                        <div class="col-md-6">
                            <a href="/reports" class="btn btn-outline-info w-100 py-3">
                                <i class="fas fa-chart-bar fa-2x d-block mb-2"></i>
                                التقارير
                            </a>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (hasRole('super_admin')): ?>
                        <div class="col-md-6">
                            <a href="/offices/create" class="btn btn-outline-secondary w-100 py-3">
                                <i class="fas fa-building fa-2x d-block mb-2"></i>
                                إضافة مكتب
                            </a>
                        </div>
                        
                        <div class="col-md-6">
                            <a href="/admin/backup" class="btn btn-outline-dark w-100 py-3">
                                <i class="fas fa-database fa-2x d-block mb-2"></i>
                                نسخ احتياطي
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
</div>

<!-- Alerts and Notifications -->
<?php if (isset($dashboardData['alerts']) && !empty($dashboardData['alerts'])): ?>
    <div class="row">
        <div class="col-12">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        تنبيهات مهمة
                    </h5>
                </div>
                <div class="card-body">
                    <?php foreach ($dashboardData['alerts'] as $alert): ?>
                        <div class="alert alert-<?= $alert['type'] ?> d-flex justify-content-between align-items-center">
                            <span><?= e($alert['message']) ?></span>
                            <?php if (isset($alert['action'])): ?>
                                <a href="<?= $alert['action'] ?>" class="btn btn-sm btn-outline-<?= $alert['type'] ?>">
                                    عرض التفاصيل
                                </a>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>

<style>
.welcome-card {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.activity-list {
    max-height: 400px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    padding: 1rem 0;
    border-bottom: 1px solid #f1f3f4;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-left: 1rem;
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.activity-description {
    color: #6c757d;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

.activity-time {
    color: #adb5bd;
    font-size: 0.75rem;
}
</style>

<script>
// Initialize dashboard charts
$(document).ready(function() {
    <?php if ($userRole !== 'super_admin'): ?>
        initializeDashboardCharts();
    <?php endif; ?>
});

function initializeDashboardCharts() {
    // Revenue Chart
    if ($('#revenueChart').length) {
        const revenueCtx = document.getElementById('revenueChart').getContext('2d');
        new Chart(revenueCtx, {
            type: 'line',
            data: {
                labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                datasets: [{
                    label: 'الإيرادات (ريال)',
                    data: [<?= implode(',', array_column($dashboardData['charts']['monthly_revenue'] ?? [], 'revenue')) ?>],
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return new Intl.NumberFormat('ar-SA').format(value) + ' ﷼';
                            }
                        }
                    }
                }
            }
        });
    }
    
    // Properties Chart
    if ($('#propertiesChart').length) {
        const propertiesCtx = document.getElementById('propertiesChart').getContext('2d');
        new Chart(propertiesCtx, {
            type: 'doughnut',
            data: {
                labels: ['متاح', 'مباع', 'مؤجر', 'قيد التفاوض'],
                datasets: [{
                    data: [
                        <?= $stats['available_properties'] ?? 0 ?>,
                        <?= $stats['sold_properties'] ?? 0 ?>,
                        <?= $stats['rented_properties'] ?? 0 ?>,
                        <?= ($stats['total_properties'] ?? 0) - ($stats['available_properties'] ?? 0) - ($stats['sold_properties'] ?? 0) - ($stats['rented_properties'] ?? 0) ?>
                    ],
                    backgroundColor: [
                        '#28a745',
                        '#dc3545', 
                        '#ffc107',
                        '#17a2b8'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }
}
</script>
