<?php
/**
 * Authentication Class
 * Handles user authentication, authorization, and JWT tokens
 */

class Auth {
    private static $user = null;
    private static $permissions = null;
    
    /**
     * Attempt to log in user
     */
    public static function attempt($email, $password, $remember = false) {
        $userModel = new User();
        $user = $userModel->findByEmail($email);
        
        if (!$user) {
            return false;
        }
        
        // Check if account is active
        if ($user['status'] !== 'active') {
            throw new Exception('الحساب غير مفعل');
        }
        
        // Verify password
        if (!password_verify($password, $user['password'])) {
            // Log failed attempt
            self::logFailedAttempt($email);
            return false;
        }
        
        // Check for too many failed attempts
        if (self::hasTooManyFailedAttempts($email)) {
            throw new Exception('تم تجاوز عدد المحاولات المسموح. يرجى المحاولة لاحقاً');
        }
        
        // Clear failed attempts
        self::clearFailedAttempts($email);
        
        // Update last login
        $userModel->updateLastLogin($user['id']);
        
        // Create session
        self::login($user, $remember);
        
        return true;
    }
    
    /**
     * Log in user
     */
    public static function login($user, $remember = false) {
        // Remove sensitive data
        unset($user['password']);
        
        // Store user in session
        Session::set('user', $user);
        Session::set('authenticated', true);
        
        // Generate JWT token
        $token = self::generateJWT($user);
        Session::set('jwt_token', $token);
        
        // Set remember me cookie if requested
        if ($remember) {
            $rememberToken = self::generateRememberToken();
            setcookie('remember_token', $rememberToken, time() + (30 * 24 * 60 * 60), '/', '', true, true);
            
            // Store remember token in database
            $userModel = new User();
            $userModel->setRememberToken($user['id'], $rememberToken);
        }
        
        self::$user = $user;
        self::loadPermissions();
    }
    
    /**
     * Log out user
     */
    public static function logout() {
        // Clear remember token if exists
        if (isset($_COOKIE['remember_token'])) {
            $userModel = new User();
            $userModel->clearRememberToken(self::id());
            setcookie('remember_token', '', time() - 3600, '/', '', true, true);
        }
        
        // Clear session
        Session::destroy();
        
        self::$user = null;
        self::$permissions = null;
    }
    
    /**
     * Check if user is authenticated
     */
    public static function check() {
        if (self::$user !== null) {
            return true;
        }
        
        // Check session
        if (Session::get('authenticated')) {
            self::$user = Session::get('user');
            self::loadPermissions();
            return true;
        }
        
        // Check remember token
        if (isset($_COOKIE['remember_token'])) {
            return self::loginByRememberToken($_COOKIE['remember_token']);
        }
        
        return false;
    }
    
    /**
     * Get authenticated user
     */
    public static function user() {
        if (!self::check()) {
            return null;
        }
        
        return self::$user;
    }
    
    /**
     * Get user ID
     */
    public static function id() {
        $user = self::user();
        return $user ? $user['id'] : null;
    }
    
    /**
     * Check if user has role
     */
    public static function hasRole($role) {
        $user = self::user();
        if (!$user) {
            return false;
        }
        
        return $user['role'] === $role;
    }
    
    /**
     * Check if user has permission
     */
    public static function hasPermission($permission) {
        if (!self::check()) {
            return false;
        }
        
        // Super admin has all permissions
        if (self::hasRole(ROLE_SUPER_ADMIN)) {
            return true;
        }
        
        if (self::$permissions === null) {
            self::loadPermissions();
        }
        
        return in_array($permission, self::$permissions);
    }
    
    /**
     * Check if user has any of the given permissions
     */
    public static function hasAnyPermission($permissions) {
        foreach ($permissions as $permission) {
            if (self::hasPermission($permission)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Check if user has all of the given permissions
     */
    public static function hasAllPermissions($permissions) {
        foreach ($permissions as $permission) {
            if (!self::hasPermission($permission)) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * Get user tenant ID
     */
    public static function tenantId() {
        $user = self::user();
        return $user ? $user['tenant_id'] : null;
    }
    
    /**
     * Generate JWT token
     */
    private static function generateJWT($user) {
        $header = json_encode(['typ' => 'JWT', 'alg' => 'HS256']);
        $payload = json_encode([
            'user_id' => $user['id'],
            'email' => $user['email'],
            'role' => $user['role'],
            'tenant_id' => $user['tenant_id'],
            'iat' => time(),
            'exp' => time() + JWT_EXPIRY
        ]);
        
        $base64Header = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($header));
        $base64Payload = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($payload));
        
        $signature = hash_hmac('sha256', $base64Header . "." . $base64Payload, JWT_SECRET, true);
        $base64Signature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));
        
        return $base64Header . "." . $base64Payload . "." . $base64Signature;
    }
    
    /**
     * Verify JWT token
     */
    public static function verifyJWT($token) {
        $parts = explode('.', $token);
        if (count($parts) !== 3) {
            return false;
        }
        
        list($header, $payload, $signature) = $parts;
        
        // Verify signature
        $expectedSignature = hash_hmac('sha256', $header . "." . $payload, JWT_SECRET, true);
        $expectedBase64Signature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($expectedSignature));
        
        if ($signature !== $expectedBase64Signature) {
            return false;
        }
        
        // Decode payload
        $payloadData = json_decode(base64_decode(str_replace(['-', '_'], ['+', '/'], $payload)), true);
        
        // Check expiration
        if ($payloadData['exp'] < time()) {
            return false;
        }
        
        return $payloadData;
    }
    
    /**
     * Generate remember token
     */
    private static function generateRememberToken() {
        return bin2hex(random_bytes(32));
    }
    
    /**
     * Login by remember token
     */
    private static function loginByRememberToken($token) {
        $userModel = new User();
        $user = $userModel->findByRememberToken($token);
        
        if (!$user || $user['status'] !== 'active') {
            return false;
        }
        
        self::login($user);
        return true;
    }
    
    /**
     * Load user permissions
     */
    private static function loadPermissions() {
        if (!self::$user) {
            return;
        }
        
        $permissionModel = new Permission();
        self::$permissions = $permissionModel->getUserPermissions(self::$user['id']);
    }
    
    /**
     * Log failed login attempt
     */
    private static function logFailedAttempt($email) {
        $key = 'failed_attempts_' . md5($email);
        $attempts = Session::get($key, 0) + 1;
        Session::set($key, $attempts);
        Session::set($key . '_time', time());
    }
    
    /**
     * Check if user has too many failed attempts
     */
    private static function hasTooManyFailedAttempts($email) {
        $key = 'failed_attempts_' . md5($email);
        $attempts = Session::get($key, 0);
        $lastAttempt = Session::get($key . '_time', 0);
        
        // Reset attempts after 15 minutes
        if (time() - $lastAttempt > 900) {
            Session::remove($key);
            Session::remove($key . '_time');
            return false;
        }
        
        return $attempts >= 5;
    }
    
    /**
     * Clear failed attempts
     */
    private static function clearFailedAttempts($email) {
        $key = 'failed_attempts_' . md5($email);
        Session::remove($key);
        Session::remove($key . '_time');
    }
    
    /**
     * Generate password hash
     */
    public static function hashPassword($password) {
        return password_hash($password, PASSWORD_BCRYPT, ['cost' => 12]);
    }
    
    /**
     * Verify password
     */
    public static function verifyPassword($password, $hash) {
        return password_verify($password, $hash);
    }
}
