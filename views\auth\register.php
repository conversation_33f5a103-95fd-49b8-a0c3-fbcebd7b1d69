<div class="row justify-content-center">
    <div class="col-md-8">
        <h2 class="text-center mb-4">إنشاء حساب جديد</h2>
        <p class="text-center text-muted mb-4">أنشئ مكتبك العقاري وابدأ إدارة عملك بكفاءة</p>
        
        <form method="POST" action="/register" id="registerForm">
            <?= csrf_field() ?>
            
            <!-- Office Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-building me-2"></i>
                        معلومات المكتب
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="office_name" class="form-label">اسم المكتب العقاري *</label>
                        <input type="text" 
                               class="form-control <?= $this->hasError('office_name') ? 'is-invalid' : '' ?>" 
                               id="office_name" 
                               name="office_name" 
                               value="<?= $this->old('office_name') ?>"
                               placeholder="مثال: مكتب الرياض العقاري"
                               required>
                        <?php if ($this->hasError('office_name')): ?>
                            <div class="invalid-feedback">
                                <?= $this->error('office_name') ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- Personal Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-user me-2"></i>
                        المعلومات الشخصية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="first_name" class="form-label">الاسم الأول *</label>
                            <input type="text" 
                                   class="form-control <?= $this->hasError('first_name') ? 'is-invalid' : '' ?>" 
                                   id="first_name" 
                                   name="first_name" 
                                   value="<?= $this->old('first_name') ?>"
                                   placeholder="الاسم الأول"
                                   required>
                            <?php if ($this->hasError('first_name')): ?>
                                <div class="invalid-feedback">
                                    <?= $this->error('first_name') ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="last_name" class="form-label">الاسم الأخير *</label>
                            <input type="text" 
                                   class="form-control <?= $this->hasError('last_name') ? 'is-invalid' : '' ?>" 
                                   id="last_name" 
                                   name="last_name" 
                                   value="<?= $this->old('last_name') ?>"
                                   placeholder="الاسم الأخير"
                                   required>
                            <?php if ($this->hasError('last_name')): ?>
                                <div class="invalid-feedback">
                                    <?= $this->error('last_name') ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني *</label>
                            <input type="email" 
                                   class="form-control <?= $this->hasError('email') ? 'is-invalid' : '' ?>" 
                                   id="email" 
                                   name="email" 
                                   value="<?= $this->old('email') ?>"
                                   placeholder="<EMAIL>"
                                   required>
                            <?php if ($this->hasError('email')): ?>
                                <div class="invalid-feedback">
                                    <?= $this->error('email') ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">رقم الهاتف *</label>
                            <input type="tel" 
                                   class="form-control <?= $this->hasError('phone') ? 'is-invalid' : '' ?>" 
                                   id="phone" 
                                   name="phone" 
                                   value="<?= $this->old('phone') ?>"
                                   placeholder="05xxxxxxxx"
                                   required>
                            <?php if ($this->hasError('phone')): ?>
                                <div class="invalid-feedback">
                                    <?= $this->error('phone') ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Security Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-shield-alt me-2"></i>
                        معلومات الأمان
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="password" class="form-label">كلمة المرور *</label>
                            <div class="input-group">
                                <input type="password" 
                                       class="form-control <?= $this->hasError('password') ? 'is-invalid' : '' ?>" 
                                       id="password" 
                                       name="password" 
                                       placeholder="كلمة مرور قوية"
                                       required>
                                <button type="button" class="btn btn-outline-secondary" id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="form-text">يجب أن تحتوي على 8 أحرف على الأقل</div>
                            <?php if ($this->hasError('password')): ?>
                                <div class="invalid-feedback d-block">
                                    <?= $this->error('password') ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="password_confirmation" class="form-label">تأكيد كلمة المرور *</label>
                            <div class="input-group">
                                <input type="password" 
                                       class="form-control" 
                                       id="password_confirmation" 
                                       name="password_confirmation" 
                                       placeholder="أعد إدخال كلمة المرور"
                                       required>
                                <button type="button" class="btn btn-outline-secondary" id="togglePasswordConfirm">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Terms and Conditions -->
            <div class="mb-4">
                <div class="form-check">
                    <input type="checkbox" 
                           class="form-check-input <?= $this->hasError('terms') ? 'is-invalid' : '' ?>" 
                           id="terms" 
                           name="terms" 
                           value="1" 
                           required>
                    <label class="form-check-label" for="terms">
                        أوافق على <a href="#" target="_blank">الشروط والأحكام</a> و <a href="#" target="_blank">سياسة الخصوصية</a>
                    </label>
                    <?php if ($this->hasError('terms')): ?>
                        <div class="invalid-feedback d-block">
                            <?= $this->error('terms') ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="d-grid mb-3">
                <button type="submit" class="btn btn-primary btn-lg">
                    <i class="fas fa-user-plus me-2"></i>
                    إنشاء الحساب
                </button>
            </div>
        </form>
        
        <div class="auth-links">
            <p class="mb-0">
                لديك حساب بالفعل؟ 
                <a href="/login">تسجيل الدخول</a>
            </p>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Toggle password visibility
    $('#togglePassword, #togglePasswordConfirm').on('click', function() {
        const targetId = $(this).attr('id') === 'togglePassword' ? '#password' : '#password_confirmation';
        const passwordField = $(targetId);
        const icon = $(this).find('i');
        
        if (passwordField.attr('type') === 'password') {
            passwordField.attr('type', 'text');
            icon.removeClass('fa-eye').addClass('fa-eye-slash');
        } else {
            passwordField.attr('type', 'password');
            icon.removeClass('fa-eye-slash').addClass('fa-eye');
        }
    });
    
    // Real-time password confirmation check
    $('#password_confirmation').on('input', function() {
        const password = $('#password').val();
        const confirmation = $(this).val();
        
        if (confirmation && password !== confirmation) {
            $(this).addClass('is-invalid');
            $(this).siblings('.invalid-feedback').remove();
            $(this).after('<div class="invalid-feedback">كلمة المرور غير متطابقة</div>');
        } else {
            $(this).removeClass('is-invalid');
            $(this).siblings('.invalid-feedback').remove();
        }
    });
    
    // Phone number formatting
    $('#phone').on('input', function() {
        let value = $(this).val().replace(/\D/g, '');
        
        // Add Saudi country code if not present
        if (value.length === 9 && value.startsWith('5')) {
            value = '966' + value;
        } else if (value.length === 10 && value.startsWith('05')) {
            value = '966' + value.substring(1);
        }
        
        $(this).val(value);
    });
    
    // Form validation
    $('#registerForm').on('submit', function(e) {
        let isValid = true;
        
        // Office name validation
        const officeName = $('#office_name').val().trim();
        if (!officeName || officeName.length < 3) {
            showFieldError('office_name', 'اسم المكتب يجب أن يكون 3 أحرف على الأقل');
            isValid = false;
        }
        
        // Email validation
        const email = $('#email').val();
        if (!email || !isValidEmail(email)) {
            showFieldError('email', 'يرجى إدخال بريد إلكتروني صحيح');
            isValid = false;
        }
        
        // Phone validation
        const phone = $('#phone').val();
        if (!phone || !isValidSaudiPhone(phone)) {
            showFieldError('phone', 'يرجى إدخال رقم هاتف سعودي صحيح');
            isValid = false;
        }
        
        // Password validation
        const password = $('#password').val();
        if (!password || password.length < 8) {
            showFieldError('password', 'كلمة المرور يجب أن تكون 8 أحرف على الأقل');
            isValid = false;
        }
        
        // Password confirmation
        const passwordConfirm = $('#password_confirmation').val();
        if (password !== passwordConfirm) {
            showFieldError('password_confirmation', 'كلمة المرور غير متطابقة');
            isValid = false;
        }
        
        // Terms acceptance
        if (!$('#terms').is(':checked')) {
            showFieldError('terms', 'يجب الموافقة على الشروط والأحكام');
            isValid = false;
        }
        
        if (!isValid) {
            e.preventDefault();
            $('html, body').animate({
                scrollTop: $('.is-invalid:first').offset().top - 100
            }, 500);
        }
    });
    
    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    
    function isValidSaudiPhone(phone) {
        const phoneRegex = /^(966|0)?5[0-9]{8}$/;
        return phoneRegex.test(phone.replace(/\s/g, ''));
    }
    
    function showFieldError(fieldName, message) {
        const field = $('#' + fieldName);
        field.addClass('is-invalid');
        
        // Remove existing error message
        field.closest('.mb-3').find('.invalid-feedback').remove();
        
        // Add new error message
        if (field.closest('.input-group').length) {
            field.closest('.input-group').after('<div class="invalid-feedback d-block">' + message + '</div>');
        } else {
            field.after('<div class="invalid-feedback">' + message + '</div>');
        }
    }
});
</script>
