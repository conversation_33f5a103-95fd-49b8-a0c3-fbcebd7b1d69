<?php
/**
 * Home Controller
 * Handles public home page and landing page
 */

class HomeController extends Controller {
    
    public function __construct() {
        parent::__construct();
    }
    
    /**
     * Show home page
     */
    public function index() {
        // If user is already authenticated, redirect to dashboard
        if (Auth::check()) {
            return $this->redirect('/dashboard');
        }
        
        // Get system statistics for landing page
        $stats = $this->getSystemStats();
        
        $this->view->set('title', 'الرئيسية - ' . APP_NAME);
        $this->view->set('description', 'نظام إدارة المكاتب العقارية المتكامل - إدارة العقارات والعملاء والفواتير بكفاءة عالية');
        $this->view->set('keywords', 'عقارات، إدارة، مكاتب، نظام، سعودية، إدارة عقارية، CRM');
        $this->view->set('stats', $stats);
        $this->view->setLayout('layouts/landing');
        $this->view->render('home/index');
    }
    
    /**
     * Get system statistics for landing page
     */
    private function getSystemStats() {
        try {
            $stats = [
                'total_offices' => 0,
                'total_properties' => 0,
                'total_clients' => 0,
                'total_transactions' => 0
            ];
            
            // Get total offices (tenants)
            $tenantModel = new Tenant();
            $stats['total_offices'] = $tenantModel->count(false); // Don't filter by tenant
            
            // Get total properties across all tenants
            $propertyModel = new Property();
            $result = $propertyModel->query("SELECT COUNT(*) as count FROM properties WHERE deleted_at IS NULL");
            $stats['total_properties'] = $result[0]['count'] ?? 0;
            
            // Get total clients across all tenants
            $clientModel = new Client();
            $result = $clientModel->query("SELECT COUNT(*) as count FROM clients WHERE deleted_at IS NULL");
            $stats['total_clients'] = $result[0]['count'] ?? 0;
            
            // Get total transactions across all tenants
            $transactionModel = new Transaction();
            $result = $transactionModel->query("SELECT COUNT(*) as count FROM transactions");
            $stats['total_transactions'] = $result[0]['count'] ?? 0;
            
            return $stats;
            
        } catch (Exception $e) {
            // Return default stats if there's an error
            return [
                'total_offices' => 150,
                'total_properties' => 5000,
                'total_clients' => 12000,
                'total_transactions' => 8500
            ];
        }
    }
    
    /**
     * Show features page
     */
    public function features() {
        $this->view->set('title', 'المميزات - ' . APP_NAME);
        $this->view->set('description', 'تعرف على مميزات نظام إدارة المكاتب العقارية المتكامل');
        $this->view->setLayout('layouts/landing');
        $this->view->render('home/features');
    }
    
    /**
     * Show pricing page
     */
    public function pricing() {
        $this->view->set('title', 'الأسعار - ' . APP_NAME);
        $this->view->set('description', 'خطط أسعار مرنة تناسب جميع أحجام المكاتب العقارية');
        $this->view->setLayout('layouts/landing');
        $this->view->render('home/pricing');
    }
    
    /**
     * Show about page
     */
    public function about() {
        $this->view->set('title', 'من نحن - ' . APP_NAME);
        $this->view->set('description', 'تعرف على فريقنا ورؤيتنا في تطوير أفضل نظام إدارة عقارية');
        $this->view->setLayout('layouts/landing');
        $this->view->render('home/about');
    }
    
    /**
     * Show contact page
     */
    public function contact() {
        $this->view->set('title', 'اتصل بنا - ' . APP_NAME);
        $this->view->set('description', 'تواصل معنا للحصول على المساعدة أو الاستفسارات');
        $this->view->setLayout('layouts/landing');
        $this->view->render('home/contact');
    }
    
    /**
     * Handle contact form submission
     */
    public function submitContact() {
        try {
            // Validate CSRF token
            CSRF::validateForm();
            
            // Validate input
            $validation = $this->validate($_POST, [
                'name' => 'required|min_length:2|max_length:100',
                'email' => 'required|email',
                'phone' => 'required|phone',
                'subject' => 'required|min_length:5|max_length:200',
                'message' => 'required|min_length:10|max_length:1000'
            ]);
            
            if (!$validation) {
                Session::setErrors($this->validator->getErrors());
                Session::flashInput($_POST);
                return $this->back();
            }
            
            // Here you would typically send an email or save to database
            // For now, we'll just show a success message
            
            return $this->back('تم إرسال رسالتك بنجاح. سنتواصل معك قريباً.', 'success');
            
        } catch (Exception $e) {
            error_log('Contact form error: ' . $e->getMessage());
            return $this->back('حدث خطأ أثناء إرسال الرسالة. يرجى المحاولة مرة أخرى.');
        }
    }
    
    /**
     * Show demo request page
     */
    public function demo() {
        $this->view->set('title', 'طلب عرض تجريبي - ' . APP_NAME);
        $this->view->set('description', 'احجز عرضاً تجريبياً مجانياً لنظام إدارة المكاتب العقارية');
        $this->view->setLayout('layouts/landing');
        $this->view->render('home/demo');
    }
    
    /**
     * Handle demo request submission
     */
    public function submitDemo() {
        try {
            // Validate CSRF token
            CSRF::validateForm();
            
            // Validate input
            $validation = $this->validate($_POST, [
                'company_name' => 'required|min_length:2|max_length:200',
                'contact_name' => 'required|min_length:2|max_length:100',
                'email' => 'required|email',
                'phone' => 'required|phone',
                'employees_count' => 'required|integer',
                'preferred_time' => 'required',
                'notes' => 'max_length:500'
            ]);
            
            if (!$validation) {
                Session::setErrors($this->validator->getErrors());
                Session::flashInput($_POST);
                return $this->back();
            }
            
            // Here you would typically save the demo request to database
            // and send notification emails
            
            return $this->back('تم تسجيل طلب العرض التجريبي بنجاح. سنتواصل معك خلال 24 ساعة.', 'success');
            
        } catch (Exception $e) {
            error_log('Demo request error: ' . $e->getMessage());
            return $this->back('حدث خطأ أثناء تسجيل الطلب. يرجى المحاولة مرة أخرى.');
        }
    }
    
    /**
     * Show terms of service
     */
    public function terms() {
        $this->view->set('title', 'الشروط والأحكام - ' . APP_NAME);
        $this->view->set('description', 'شروط وأحكام استخدام نظام إدارة المكاتب العقارية');
        $this->view->setLayout('layouts/landing');
        $this->view->render('home/terms');
    }
    
    /**
     * Show privacy policy
     */
    public function privacy() {
        $this->view->set('title', 'سياسة الخصوصية - ' . APP_NAME);
        $this->view->set('description', 'سياسة الخصوصية وحماية البيانات في نظام إدارة المكاتب العقارية');
        $this->view->setLayout('layouts/landing');
        $this->view->render('home/privacy');
    }
    
    /**
     * Show FAQ page
     */
    public function faq() {
        $faqs = $this->getFAQs();
        
        $this->view->set('title', 'الأسئلة الشائعة - ' . APP_NAME);
        $this->view->set('description', 'إجابات على الأسئلة الأكثر شيوعاً حول نظام إدارة المكاتب العقارية');
        $this->view->set('faqs', $faqs);
        $this->view->setLayout('layouts/landing');
        $this->view->render('home/faq');
    }
    
    /**
     * Get frequently asked questions
     */
    private function getFAQs() {
        return [
            [
                'question' => 'ما هو نظام إدارة المكاتب العقارية؟',
                'answer' => 'هو نظام متكامل يساعد المكاتب العقارية على إدارة عملياتها بكفاءة، من إدارة العقارات والعملاء إلى الفواتير والتقارير.'
            ],
            [
                'question' => 'هل يدعم النظام عدة مكاتب؟',
                'answer' => 'نعم، النظام مصمم ليدعم عدة مكاتب عقارية مع عزل كامل للبيانات بين كل مكتب وآخر.'
            ],
            [
                'question' => 'هل يمكنني تجربة النظام مجاناً؟',
                'answer' => 'نعم، نوفر فترة تجريبية مجانية لمدة 30 يوماً مع جميع المميزات.'
            ],
            [
                'question' => 'هل البيانات آمنة؟',
                'answer' => 'نعم، نستخدم أحدث تقنيات الأمان والتشفير لحماية بياناتك، مع نسخ احتياطية يومية.'
            ],
            [
                'question' => 'هل يدعم النظام اللغة العربية؟',
                'answer' => 'نعم، النظام مصمم خصيصاً للسوق العربي ويدعم اللغة العربية بالكامل.'
            ],
            [
                'question' => 'كيف يمكنني الحصول على الدعم الفني؟',
                'answer' => 'نوفر دعماً فنياً على مدار الساعة عبر الهاتف والبريد الإلكتروني والدردشة المباشرة.'
            ]
        ];
    }
}
