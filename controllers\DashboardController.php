<?php
/**
 * Dashboard Controller
 * Handles main dashboard and overview pages
 */

class DashboardController extends Controller {
    
    public function __construct() {
        parent::__construct();
    }
    
    /**
     * Show main dashboard
     */
    public function index() {
        $user = Auth::user();
        $tenantId = Auth::tenantId();
        
        // Get dashboard data based on user role
        if (Auth::hasRole(ROLE_SUPER_ADMIN)) {
            $data = $this->getSuperAdminDashboard();
        } else {
            $data = $this->getTenantDashboard($tenantId);
        }
        
        $this->view->set('title', 'لوحة التحكم - ' . APP_NAME);
        $this->view->set('dashboardData', $data);
        $this->view->set('userRole', $user['role']);
        $this->view->render('dashboard/index');
    }
    
    /**
     * Get super admin dashboard data
     */
    private function getSuperAdminDashboard() {
        $data = [];
        
        // System statistics
        $tenantModel = new Tenant();
        $userModel = new User();
        
        $data['stats'] = [
            'total_tenants' => $tenantModel->count(false),
            'active_tenants' => count($tenantModel->getActive()),
            'total_users' => $userModel->count(false),
            'total_properties' => $this->getTotalProperties(),
            'total_clients' => $this->getTotalClients(),
            'total_invoices' => $this->getTotalInvoices()
        ];
        
        // Recent tenants
        $data['recent_tenants'] = $tenantModel->query(
            "SELECT * FROM tenants WHERE deleted_at IS NULL ORDER BY created_at DESC LIMIT 5"
        );
        
        // System activity
        $data['recent_activity'] = $this->getSystemActivity();
        
        // Expiring subscriptions
        $data['expiring_subscriptions'] = $tenantModel->getExpiringSubscriptions(30);
        
        // Monthly statistics
        $data['monthly_stats'] = $this->getMonthlyStats();
        
        return $data;
    }
    
    /**
     * Get tenant dashboard data
     */
    private function getTenantDashboard($tenantId) {
        $data = [];
        
        // Tenant statistics
        $propertyModel = new Property();
        $clientModel = new Client();
        $invoiceModel = new Invoice();
        
        $data['stats'] = [
            'total_properties' => $propertyModel->count($tenantId),
            'available_properties' => $this->getPropertiesByStatus($tenantId, 'متاح'),
            'sold_properties' => $this->getPropertiesByStatus($tenantId, 'مباع'),
            'rented_properties' => $this->getPropertiesByStatus($tenantId, 'مؤجر'),
            'total_clients' => $clientModel->count($tenantId),
            'active_clients' => $this->getClientsByStatus($tenantId, 'active'),
            'total_invoices' => $invoiceModel->count($tenantId),
            'paid_invoices' => $this->getInvoicesByStatus($tenantId, 'مدفوعة'),
            'pending_invoices' => $this->getInvoicesByStatus($tenantId, 'مرسلة'),
            'overdue_invoices' => $this->getOverdueInvoices($tenantId)
        ];
        
        // Financial summary
        $data['financial'] = [
            'revenue_this_month' => $this->getRevenueThisMonth($tenantId),
            'revenue_last_month' => $this->getRevenueLastMonth($tenantId),
            'total_revenue' => $this->getTotalRevenue($tenantId),
            'pending_amount' => $this->getPendingAmount($tenantId)
        ];
        
        // Recent activities
        $data['recent_properties'] = $propertyModel->query(
            "SELECT * FROM properties WHERE tenant_id = ? AND deleted_at IS NULL ORDER BY created_at DESC LIMIT 5",
            [$tenantId]
        );
        
        $data['recent_clients'] = $clientModel->query(
            "SELECT * FROM clients WHERE tenant_id = ? AND deleted_at IS NULL ORDER BY created_at DESC LIMIT 5",
            [$tenantId]
        );
        
        $data['recent_invoices'] = $invoiceModel->query(
            "SELECT * FROM invoices WHERE tenant_id = ? AND deleted_at IS NULL ORDER BY created_at DESC LIMIT 5",
            [$tenantId]
        );
        
        // Charts data
        $data['charts'] = [
            'properties_by_type' => $this->getPropertiesByType($tenantId),
            'properties_by_status' => $this->getPropertiesByStatusChart($tenantId),
            'clients_by_type' => $this->getClientsByType($tenantId),
            'monthly_revenue' => $this->getMonthlyRevenue($tenantId),
            'monthly_transactions' => $this->getMonthlyTransactions($tenantId)
        ];
        
        // Notifications and alerts
        $data['alerts'] = $this->getTenantAlerts($tenantId);
        
        return $data;
    }
    
    /**
     * Get properties by status
     */
    private function getPropertiesByStatus($tenantId, $status) {
        $propertyModel = new Property();
        return $propertyModel->query(
            "SELECT COUNT(*) as count FROM properties WHERE tenant_id = ? AND status = ? AND deleted_at IS NULL",
            [$tenantId, $status]
        )[0]['count'];
    }
    
    /**
     * Get clients by status
     */
    private function getClientsByStatus($tenantId, $status) {
        $clientModel = new Client();
        return $clientModel->query(
            "SELECT COUNT(*) as count FROM clients WHERE tenant_id = ? AND status = ? AND deleted_at IS NULL",
            [$tenantId, $status]
        )[0]['count'];
    }
    
    /**
     * Get invoices by status
     */
    private function getInvoicesByStatus($tenantId, $status) {
        $invoiceModel = new Invoice();
        return $invoiceModel->query(
            "SELECT COUNT(*) as count FROM invoices WHERE tenant_id = ? AND status = ? AND deleted_at IS NULL",
            [$tenantId, $status]
        )[0]['count'];
    }
    
    /**
     * Get overdue invoices count
     */
    private function getOverdueInvoices($tenantId) {
        $invoiceModel = new Invoice();
        return $invoiceModel->query(
            "SELECT COUNT(*) as count FROM invoices 
             WHERE tenant_id = ? AND status IN ('مرسلة') AND due_date < CURDATE() AND deleted_at IS NULL",
            [$tenantId]
        )[0]['count'];
    }
    
    /**
     * Get revenue for current month
     */
    private function getRevenueThisMonth($tenantId) {
        $invoiceModel = new Invoice();
        $result = $invoiceModel->query(
            "SELECT COALESCE(SUM(total_amount), 0) as total 
             FROM invoices 
             WHERE tenant_id = ? AND status = 'مدفوعة' 
             AND MONTH(paid_date) = MONTH(CURRENT_DATE()) 
             AND YEAR(paid_date) = YEAR(CURRENT_DATE())
             AND deleted_at IS NULL",
            [$tenantId]
        );
        
        return $result[0]['total'];
    }
    
    /**
     * Get revenue for last month
     */
    private function getRevenueLastMonth($tenantId) {
        $invoiceModel = new Invoice();
        $result = $invoiceModel->query(
            "SELECT COALESCE(SUM(total_amount), 0) as total 
             FROM invoices 
             WHERE tenant_id = ? AND status = 'مدفوعة' 
             AND MONTH(paid_date) = MONTH(DATE_SUB(CURRENT_DATE(), INTERVAL 1 MONTH))
             AND YEAR(paid_date) = YEAR(DATE_SUB(CURRENT_DATE(), INTERVAL 1 MONTH))
             AND deleted_at IS NULL",
            [$tenantId]
        );
        
        return $result[0]['total'];
    }
    
    /**
     * Get total revenue
     */
    private function getTotalRevenue($tenantId) {
        $invoiceModel = new Invoice();
        $result = $invoiceModel->query(
            "SELECT COALESCE(SUM(total_amount), 0) as total 
             FROM invoices 
             WHERE tenant_id = ? AND status = 'مدفوعة' AND deleted_at IS NULL",
            [$tenantId]
        );
        
        return $result[0]['total'];
    }
    
    /**
     * Get pending amount
     */
    private function getPendingAmount($tenantId) {
        $invoiceModel = new Invoice();
        $result = $invoiceModel->query(
            "SELECT COALESCE(SUM(total_amount - paid_amount), 0) as total 
             FROM invoices 
             WHERE tenant_id = ? AND status IN ('مرسلة', 'متأخرة') AND deleted_at IS NULL",
            [$tenantId]
        );
        
        return $result[0]['total'];
    }
    
    /**
     * Get properties by type for charts
     */
    private function getPropertiesByType($tenantId) {
        $propertyModel = new Property();
        return $propertyModel->query(
            "SELECT type, COUNT(*) as count 
             FROM properties 
             WHERE tenant_id = ? AND deleted_at IS NULL 
             GROUP BY type",
            [$tenantId]
        );
    }
    
    /**
     * Get properties by status for charts
     */
    private function getPropertiesByStatusChart($tenantId) {
        $propertyModel = new Property();
        return $propertyModel->query(
            "SELECT status, COUNT(*) as count 
             FROM properties 
             WHERE tenant_id = ? AND deleted_at IS NULL 
             GROUP BY status",
            [$tenantId]
        );
    }
    
    /**
     * Get clients by type for charts
     */
    private function getClientsByType($tenantId) {
        $clientModel = new Client();
        return $clientModel->query(
            "SELECT type, COUNT(*) as count 
             FROM clients 
             WHERE tenant_id = ? AND deleted_at IS NULL 
             GROUP BY type",
            [$tenantId]
        );
    }
    
    /**
     * Get monthly revenue for charts
     */
    private function getMonthlyRevenue($tenantId) {
        $invoiceModel = new Invoice();
        return $invoiceModel->query(
            "SELECT 
                DATE_FORMAT(paid_date, '%Y-%m') as month,
                SUM(total_amount) as revenue
             FROM invoices 
             WHERE tenant_id = ? AND status = 'مدفوعة' 
             AND paid_date >= DATE_SUB(CURRENT_DATE(), INTERVAL 12 MONTH)
             AND deleted_at IS NULL
             GROUP BY DATE_FORMAT(paid_date, '%Y-%m')
             ORDER BY month",
            [$tenantId]
        );
    }
    
    /**
     * Get monthly transactions for charts
     */
    private function getMonthlyTransactions($tenantId) {
        $transactionModel = new Transaction();
        return $transactionModel->query(
            "SELECT 
                DATE_FORMAT(date, '%Y-%m') as month,
                COUNT(*) as count
             FROM transactions 
             WHERE tenant_id = ? 
             AND date >= DATE_SUB(CURRENT_DATE(), INTERVAL 12 MONTH)
             GROUP BY DATE_FORMAT(date, '%Y-%m')
             ORDER BY month",
            [$tenantId]
        );
    }
    
    /**
     * Get tenant alerts and notifications
     */
    private function getTenantAlerts($tenantId) {
        $alerts = [];
        
        // Overdue invoices
        $overdueCount = $this->getOverdueInvoices($tenantId);
        if ($overdueCount > 0) {
            $alerts[] = [
                'type' => 'warning',
                'message' => "لديك {$overdueCount} فاتورة متأخرة الدفع",
                'action' => '/invoices?status=overdue'
            ];
        }
        
        // Properties without images
        $propertyModel = new Property();
        $propertiesWithoutImages = $propertyModel->query(
            "SELECT COUNT(*) as count FROM properties 
             WHERE tenant_id = ? AND (images IS NULL OR images = '[]') AND deleted_at IS NULL",
            [$tenantId]
        )[0]['count'];
        
        if ($propertiesWithoutImages > 0) {
            $alerts[] = [
                'type' => 'info',
                'message' => "لديك {$propertiesWithoutImages} عقار بدون صور",
                'action' => '/properties?filter=no_images'
            ];
        }
        
        return $alerts;
    }
    
    /**
     * Helper methods for super admin dashboard
     */
    private function getTotalProperties() {
        $propertyModel = new Property();
        return $propertyModel->query("SELECT COUNT(*) as count FROM properties WHERE deleted_at IS NULL")[0]['count'];
    }
    
    private function getTotalClients() {
        $clientModel = new Client();
        return $clientModel->query("SELECT COUNT(*) as count FROM clients WHERE deleted_at IS NULL")[0]['count'];
    }
    
    private function getTotalInvoices() {
        $invoiceModel = new Invoice();
        return $invoiceModel->query("SELECT COUNT(*) as count FROM invoices WHERE deleted_at IS NULL")[0]['count'];
    }
    
    private function getSystemActivity() {
        $activityModel = new ActivityLog();
        return $activityModel->query(
            "SELECT * FROM activity_logs ORDER BY created_at DESC LIMIT 10"
        );
    }
    
    private function getMonthlyStats() {
        // Implementation for monthly statistics across all tenants
        return [];
    }
}
