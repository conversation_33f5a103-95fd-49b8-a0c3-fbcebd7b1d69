<?php
/**
 * PSR-4 Autoloader for Real Estate SaaS System
 * Automatically loads classes based on namespace and file structure
 */

class Autoloader {
    private static $namespaces = [];
    
    public static function register() {
        spl_autoload_register([__CLASS__, 'loadClass']);
        
        // Register namespaces
        self::addNamespace('Controllers', CONTROLLERS_PATH);
        self::addNamespace('Models', MODELS_PATH);
        self::addNamespace('Core', ROOT_PATH . '/core');
        self::addNamespace('Helpers', ROOT_PATH . '/helpers');
        self::addNamespace('Middleware', ROOT_PATH . '/middleware');
        self::addNamespace('Api', ROOT_PATH . '/api');
    }
    
    public static function addNamespace($namespace, $path) {
        self::$namespaces[$namespace] = rtrim($path, '/') . '/';
    }
    
    public static function loadClass($className) {
        // Remove leading backslash
        $className = ltrim($className, '\\');
        
        // Check if class has namespace
        if (strpos($className, '\\') !== false) {
            $parts = explode('\\', $className);
            $namespace = array_shift($parts);
            $className = implode('\\', $parts);
            
            if (isset(self::$namespaces[$namespace])) {
                $filePath = self::$namespaces[$namespace] . str_replace('\\', '/', $className) . '.php';
                if (file_exists($filePath)) {
                    require_once $filePath;
                    return true;
                }
            }
        }
        
        // Try to load from different directories
        $directories = [
            CONTROLLERS_PATH,
            MODELS_PATH,
            ROOT_PATH . '/core',
            ROOT_PATH . '/helpers',
            ROOT_PATH . '/middleware'
        ];
        
        foreach ($directories as $directory) {
            $filePath = $directory . '/' . $className . '.php';
            if (file_exists($filePath)) {
                require_once $filePath;
                return true;
            }
        }
        
        return false;
    }
}

// Register the autoloader
Autoloader::register();

// Load core classes manually
require_once ROOT_PATH . '/core/Router.php';
require_once ROOT_PATH . '/core/Controller.php';
require_once ROOT_PATH . '/core/Model.php';
require_once ROOT_PATH . '/core/View.php';
require_once ROOT_PATH . '/core/Auth.php';
require_once ROOT_PATH . '/core/Validator.php';
require_once ROOT_PATH . '/core/Session.php';
require_once ROOT_PATH . '/core/CSRF.php';
require_once ROOT_PATH . '/helpers/Helper.php';
