<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>خطأ في النظام - <?= APP_NAME ?? 'نظام إدارة المكاتب العقارية' ?></title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Arabic Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 20px;
        }
        
        .error-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 3rem;
            text-align: center;
            max-width: 600px;
            width: 100%;
        }
        
        .error-icon {
            font-size: 5rem;
            color: #dc3545;
            margin-bottom: 1.5rem;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        .error-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 1rem;
        }
        
        .error-message {
            font-size: 1.2rem;
            color: #6c757d;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        
        .error-code {
            font-size: 1rem;
            color: #adb5bd;
            margin-bottom: 2rem;
            font-family: 'Courier New', monospace;
            background: #f8f9fa;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            display: inline-block;
        }
        
        .btn-home {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            color: white;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            margin: 0.5rem;
        }
        
        .btn-home:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            color: white;
            text-decoration: none;
        }
        
        .btn-secondary {
            background: #6c757d;
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            color: white;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            margin: 0.5rem;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
            color: white;
            text-decoration: none;
        }
        
        .error-details {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-top: 2rem;
            text-align: right;
            border-right: 4px solid #dc3545;
        }
        
        .error-details h5 {
            color: #dc3545;
            margin-bottom: 1rem;
            font-weight: 600;
        }
        
        .error-details p {
            color: #6c757d;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }
        
        .support-info {
            background: #e3f2fd;
            border-radius: 10px;
            padding: 1.5rem;
            margin-top: 1.5rem;
            border-right: 4px solid #2196f3;
        }
        
        .support-info h6 {
            color: #1976d2;
            margin-bottom: 1rem;
            font-weight: 600;
        }
        
        .support-info p {
            color: #424242;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }
        
        @media (max-width: 768px) {
            .error-container {
                padding: 2rem 1.5rem;
            }
            
            .error-title {
                font-size: 2rem;
            }
            
            .error-message {
                font-size: 1rem;
            }
            
            .btn-home, .btn-secondary {
                display: block;
                margin: 0.5rem 0;
                width: 100%;
            }
        }
    </style>
</head>
<body>
    
    <div class="error-container">
        <div class="error-icon">
            <i class="fas fa-exclamation-triangle"></i>
        </div>
        
        <h1 class="error-title">خطأ في النظام</h1>
        
        <p class="error-message">
            عذراً، حدث خطأ غير متوقع في النظام. نحن نعمل على حل هذه المشكلة.
        </p>
        
        <div class="error-code">
            رمز الخطأ: 500 - خطأ داخلي في الخادم
        </div>
        
        <div class="action-buttons">
            <a href="/" class="btn-home">
                <i class="fas fa-home me-2"></i>
                العودة للرئيسية
            </a>
            
            <a href="javascript:history.back()" class="btn-secondary">
                <i class="fas fa-arrow-right me-2"></i>
                العودة للصفحة السابقة
            </a>
        </div>
        
        <div class="error-details">
            <h5>
                <i class="fas fa-info-circle me-2"></i>
                ماذا حدث؟
            </h5>
            <p>• حدث خطأ غير متوقع أثناء معالجة طلبك</p>
            <p>• قد يكون السبب مشكلة مؤقتة في الخادم</p>
            <p>• أو قد يكون هناك خطأ في البرمجة نحن نعمل على إصلاحه</p>
        </div>
        
        <div class="support-info">
            <h6>
                <i class="fas fa-headset me-2"></i>
                هل تحتاج مساعدة؟
            </h6>
            <p>
                <i class="fas fa-envelope me-2"></i>
                البريد الإلكتروني: <EMAIL>
            </p>
            <p>
                <i class="fas fa-phone me-2"></i>
                الهاتف: +966 50 123 4567
            </p>
            <p>
                <i class="fas fa-clock me-2"></i>
                ساعات العمل: من الأحد إلى الخميس، 9 صباحاً - 6 مساءً
            </p>
        </div>
        
        <div class="mt-3">
            <small class="text-muted">
                إذا استمرت المشكلة، يرجى التواصل مع فريق الدعم الفني
            </small>
        </div>
    </div>
    
    <script>
        // Auto-refresh after 30 seconds
        setTimeout(function() {
            if (confirm('هل تريد إعادة تحميل الصفحة؟')) {
                location.reload();
            }
        }, 30000);
        
        // Log error for debugging (if console is available)
        if (typeof console !== 'undefined') {
            console.error('500 Internal Server Error occurred at:', new Date().toISOString());
        }
    </script>
    
</body>
</html>
