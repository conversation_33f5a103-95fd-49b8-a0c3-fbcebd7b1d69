<?php
/**
 * Helper Functions
 * Global utility functions for the application
 */

/**
 * Escape HTML output
 */
function e($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

/**
 * Generate URL
 */
function url($path = '') {
    return rtrim(APP_URL, '/') . '/' . ltrim($path, '/');
}

/**
 * Generate asset URL
 */
function asset($path) {
    return url('assets/' . ltrim($path, '/'));
}

/**
 * Generate upload URL
 */
function upload($path) {
    return url('uploads/' . ltrim($path, '/'));
}

/**
 * Redirect to URL
 */
function redirect($path, $statusCode = 302) {
    header('Location: ' . url($path), true, $statusCode);
    exit;
}

/**
 * Redirect back
 */
function back() {
    $referer = $_SERVER['HTTP_REFERER'] ?? url('/dashboard');
    header('Location: ' . $referer);
    exit;
}

/**
 * Get old input value
 */
function old($key, $default = '') {
    return Session::getOldInput($key, $default);
}

/**
 * Generate CSRF field
 */
function csrf_field() {
    return '<input type="hidden" name="' . CSRF_TOKEN_NAME . '" value="' . CSRF::token() . '">';
}

/**
 * Generate CSRF token
 */
function csrf_token() {
    return CSRF::token();
}

/**
 * Check if user has permission
 */
function can($permission) {
    return Auth::can($permission);
}

/**
 * Check if user has role
 */
function hasRole($role) {
    return Auth::hasRole($role);
}

/**
 * Get current user
 */
function user() {
    return Auth::user();
}

/**
 * Convert numbers to Arabic numerals
 */
function arabicNumber($number) {
    $arabic = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    $english = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];

    return str_replace($english, $arabic, $number);
}

/**
 * Convert Arabic numerals to English
 */
function englishNumber($number) {
    $arabic = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    $english = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];

    return str_replace($arabic, $english, $number);
}

/**
 * Format currency in Arabic
 */
function currency($amount, $currency = 'SAR') {
    $formatted = number_format($amount, 2);
    $formatted = arabicNumber($formatted);

    switch ($currency) {
        case 'SAR':
            return $formatted . ' ريال';
        case 'USD':
            return '$' . $formatted;
        case 'EUR':
            return '€' . $formatted;
        default:
            return $formatted . ' ' . $currency;
    }
}

/**
 * Format date in Arabic
 */
function arabicDate($date, $includeTime = false) {
    $months = [
        1 => 'يناير', 2 => 'فبراير', 3 => 'مارس', 4 => 'أبريل',
        5 => 'مايو', 6 => 'يونيو', 7 => 'يوليو', 8 => 'أغسطس',
        9 => 'سبتمبر', 10 => 'أكتوبر', 11 => 'نوفمبر', 12 => 'ديسمبر'
    ];

    $timestamp = is_string($date) ? strtotime($date) : $date;

    $day = arabicNumber(date('d', $timestamp));
    $month = $months[(int)date('m', $timestamp)];
    $year = arabicNumber(date('Y', $timestamp));

    $formatted = $day . ' ' . $month . ' ' . $year;

    if ($includeTime) {
        $hour = arabicNumber(date('H', $timestamp));
        $minute = arabicNumber(date('i', $timestamp));
        $formatted .= ' - ' . $hour . ':' . $minute;
    }

    return $formatted;
}

/**
 * Time ago in Arabic
 */
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);

    if ($time < 60) {
        return 'منذ لحظات';
    } elseif ($time < 3600) {
        $minutes = floor($time / 60);
        return 'منذ ' . arabicNumber($minutes) . ' دقيقة';
    } elseif ($time < 86400) {
        $hours = floor($time / 3600);
        return 'منذ ' . arabicNumber($hours) . ' ساعة';
    } elseif ($time < 2592000) {
        $days = floor($time / 86400);
        return 'منذ ' . arabicNumber($days) . ' يوم';
    } elseif ($time < 31536000) {
        $months = floor($time / 2592000);
        return 'منذ ' . arabicNumber($months) . ' شهر';
    } else {
        $years = floor($time / 31536000);
        return 'منذ ' . arabicNumber($years) . ' سنة';
    }
}

/**
 * Generate slug from Arabic text
 */
function slug($text) {
    // Convert Arabic text to Latin
    $transliteration = [
        'ا' => 'a', 'ب' => 'b', 'ت' => 't', 'ث' => 'th', 'ج' => 'j',
        'ح' => 'h', 'خ' => 'kh', 'د' => 'd', 'ذ' => 'dh', 'ر' => 'r',
        'ز' => 'z', 'س' => 's', 'ش' => 'sh', 'ص' => 's', 'ض' => 'd',
        'ط' => 't', 'ظ' => 'z', 'ع' => 'a', 'غ' => 'gh', 'ف' => 'f',
        'ق' => 'q', 'ك' => 'k', 'ل' => 'l', 'م' => 'm', 'ن' => 'n',
        'ه' => 'h', 'و' => 'w', 'ي' => 'y', 'ى' => 'a', 'ة' => 'h',
        'أ' => 'a', 'إ' => 'i', 'آ' => 'a', 'ؤ' => 'o', 'ئ' => 'e'
    ];

    // Replace Arabic characters
    $text = strtr($text, $transliteration);

    // Convert to lowercase
    $text = strtolower($text);

    // Replace spaces and special characters with hyphens
    $text = preg_replace('/[^a-z0-9]+/', '-', $text);

    // Remove leading/trailing hyphens
    $text = trim($text, '-');

    return $text;
}

/**
 * Validate Saudi phone number
 */
function isValidSaudiPhone($phone) {
    // Remove all non-digits
    $phone = preg_replace('/\D/', '', $phone);

    // Check if it's a valid Saudi phone number
    return preg_match('/^(966|0)?5[0-9]{8}$/', $phone);
}

/**
 * Format Saudi phone number
 */
function formatSaudiPhone($phone) {
    // Remove all non-digits
    $phone = preg_replace('/\D/', '', $phone);

    // Add country code if missing
    if (strlen($phone) == 9 && substr($phone, 0, 1) == '5') {
        $phone = '966' . $phone;
    } elseif (strlen($phone) == 10 && substr($phone, 0, 2) == '05') {
        $phone = '966' . substr($phone, 1);
    }

    // Format as +966 5X XXX XXXX
    if (strlen($phone) == 12 && substr($phone, 0, 3) == '966') {
        return '+966 ' . substr($phone, 3, 2) . ' ' . substr($phone, 5, 3) . ' ' . substr($phone, 8, 4);
    }

    return $phone;
}

/**
 * Generate random string
 */
function randomString($length = 10) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';

    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }

    return $randomString;
}

/**
 * Check if request is AJAX
 */
function isAjax() {
    return isset($_SERVER['HTTP_X_REQUESTED_WITH']) &&
           strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
}

/**
 * Get client IP address
 */
function getClientIP() {
    $ipKeys = ['HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR'];

    foreach ($ipKeys as $key) {
        if (array_key_exists($key, $_SERVER) === true) {
            foreach (explode(',', $_SERVER[$key]) as $ip) {
                $ip = trim($ip);

                if (filter_var($ip, FILTER_VALIDATE_IP,
                    FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                    return $ip;
                }
            }
        }
    }

    return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
}

/**
 * Upload file
 */
function uploadFile($file, $directory = 'uploads', $allowedTypes = ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx']) {
    if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
        throw new Exception('ملف غير صالح');
    }

    // Check file size (max 10MB)
    if ($file['size'] > 10 * 1024 * 1024) {
        throw new Exception('حجم الملف كبير جداً (الحد الأقصى 10 ميجابايت)');
    }

    // Get file extension
    $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));

    // Check allowed types
    if (!in_array($extension, $allowedTypes)) {
        throw new Exception('نوع الملف غير مسموح');
    }

    // Generate unique filename
    $filename = uniqid() . '_' . time() . '.' . $extension;

    // Create directory if it doesn't exist
    $uploadPath = ROOT_PATH . '/' . $directory;
    if (!is_dir($uploadPath)) {
        mkdir($uploadPath, 0755, true);
    }

    // Move uploaded file
    $filePath = $uploadPath . '/' . $filename;
    if (!move_uploaded_file($file['tmp_name'], $filePath)) {
        throw new Exception('فشل في رفع الملف');
    }

    return $directory . '/' . $filename;
}

/**
 * Delete file
 */
function deleteFile($filePath) {
    $fullPath = ROOT_PATH . '/' . ltrim($filePath, '/');

    if (file_exists($fullPath)) {
        return unlink($fullPath);
    }

    return false;
}

/**
 * Format file size
 */
function formatFileSize($bytes) {
    $units = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];

    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }

    return round($bytes, 2) . ' ' . $units[$i];
}
}

/**
 * Get validation error
 */
function error($key) {
    return Session::getError($key);
}

/**
 * Check if validation error exists
 */
function hasError($key) {
    return Session::hasError($key);
}

/**
 * Get flash message
 */
function flash($type) {
    return Session::getFlash($type);
}

/**
 * Check if flash message exists
 */
function hasFlash($type) {
    return Session::hasFlash($type);
}

/**
 * Generate CSRF token field
 */
function csrf_field() {
    return CSRF::field();
}

/**
 * Generate method field for forms
 */
function method_field($method) {
    return "<input type=\"hidden\" name=\"_method\" value=\"{$method}\">";
}

/**
 * Format currency
 */
function currency($amount, $currency = null) {
    $currency = $currency ?: DEFAULT_CURRENCY;
    $symbol = CURRENCY_SYMBOL;
    
    $formatted = number_format($amount, 2, '.', '٬');
    return "{$formatted} {$symbol}";
}

/**
 * Format number with Arabic numerals
 */
function arabicNumber($number) {
    $arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    $englishNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    
    $formatted = number_format($number, 0, '.', '٬');
    return str_replace($englishNumbers, $arabicNumbers, $formatted);
}

/**
 * Format date in Arabic
 */
function arabicDate($date, $includeTime = false) {
    if (!$date) return '';
    
    $timestamp = is_string($date) ? strtotime($date) : $date;
    $day = date('j', $timestamp);
    $month = GREGORIAN_MONTHS[date('n', $timestamp)];
    $year = date('Y', $timestamp);
    
    $formatted = "{$day} {$month} {$year}";
    
    if ($includeTime) {
        $time = date('H:i', $timestamp);
        $formatted .= " الساعة {$time}";
    }
    
    return $formatted;
}

/**
 * Time ago in Arabic
 */
function timeAgo($date) {
    if (!$date) return '';
    
    $timestamp = is_string($date) ? strtotime($date) : $date;
    $diff = time() - $timestamp;
    
    if ($diff < 60) {
        return 'منذ لحظات';
    } elseif ($diff < 3600) {
        $minutes = floor($diff / 60);
        return "منذ {$minutes} دقيقة";
    } elseif ($diff < 86400) {
        $hours = floor($diff / 3600);
        return "منذ {$hours} ساعة";
    } elseif ($diff < 2592000) {
        $days = floor($diff / 86400);
        return "منذ {$days} يوم";
    } else {
        return arabicDate($date);
    }
}

/**
 * Truncate text
 */
function truncate($text, $length = 100, $suffix = '...') {
    if (mb_strlen($text, 'UTF-8') <= $length) {
        return $text;
    }
    
    return mb_substr($text, 0, $length, 'UTF-8') . $suffix;
}

/**
 * Generate random string
 */
function randomString($length = 10) {
    return bin2hex(random_bytes($length / 2));
}

/**
 * Check if string is JSON
 */
function isJson($string) {
    json_decode($string);
    return json_last_error() === JSON_ERROR_NONE;
}

/**
 * Convert array to JSON
 */
function toJson($data) {
    return json_encode($data, JSON_UNESCAPED_UNICODE);
}

/**
 * Convert JSON to array
 */
function fromJson($json) {
    return json_decode($json, true);
}

/**
 * Log message
 */
function logMessage($message, $level = 'INFO') {
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[{$timestamp}] {$level}: {$message}" . PHP_EOL;
    
    $logFile = LOGS_PATH . '/app.log';
    
    // Create logs directory if it doesn't exist
    if (!is_dir(LOGS_PATH)) {
        mkdir(LOGS_PATH, 0755, true);
    }
    
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
}

/**
 * Debug dump
 */
function dd(...$vars) {
    echo '<pre style="background: #f4f4f4; padding: 10px; border: 1px solid #ddd; direction: ltr;">';
    foreach ($vars as $var) {
        var_dump($var);
    }
    echo '</pre>';
    die();
}

/**
 * Dump without die
 */
function dump(...$vars) {
    echo '<pre style="background: #f4f4f4; padding: 10px; border: 1px solid #ddd; direction: ltr;">';
    foreach ($vars as $var) {
        var_dump($var);
    }
    echo '</pre>';
}

/**
 * Get file size in human readable format
 */
function humanFileSize($bytes, $decimals = 2) {
    $size = ['B', 'KB', 'MB', 'GB', 'TB'];
    $factor = floor((strlen($bytes) - 1) / 3);
    
    return sprintf("%.{$decimals}f", $bytes / pow(1024, $factor)) . ' ' . $size[$factor];
}

/**
 * Sanitize filename
 */
function sanitizeFilename($filename) {
    // Remove special characters
    $filename = preg_replace('/[^a-zA-Z0-9\-_\.]/', '', $filename);
    
    // Remove multiple dots
    $filename = preg_replace('/\.+/', '.', $filename);
    
    return $filename;
}

/**
 * Generate slug from Arabic text
 */
function slug($text, $separator = '-') {
    // Convert Arabic text to Latin
    $text = transliterate($text);
    
    // Convert to lowercase
    $text = strtolower($text);
    
    // Remove special characters
    $text = preg_replace('/[^a-z0-9\s\-_]/', '', $text);
    
    // Replace spaces and underscores with separator
    $text = preg_replace('/[\s\-_]+/', $separator, $text);
    
    // Remove leading/trailing separators
    return trim($text, $separator);
}

/**
 * Transliterate Arabic text to Latin
 */
function transliterate($text) {
    $arabic = [
        'ا' => 'a', 'ب' => 'b', 'ت' => 't', 'ث' => 'th', 'ج' => 'j',
        'ح' => 'h', 'خ' => 'kh', 'د' => 'd', 'ذ' => 'dh', 'ر' => 'r',
        'ز' => 'z', 'س' => 's', 'ش' => 'sh', 'ص' => 's', 'ض' => 'd',
        'ط' => 't', 'ظ' => 'z', 'ع' => 'a', 'غ' => 'gh', 'ف' => 'f',
        'ق' => 'q', 'ك' => 'k', 'ل' => 'l', 'م' => 'm', 'ن' => 'n',
        'ه' => 'h', 'و' => 'w', 'ي' => 'y', 'ى' => 'a', 'ة' => 'h',
        'أ' => 'a', 'إ' => 'i', 'آ' => 'a', 'ؤ' => 'o', 'ئ' => 'e'
    ];
    
    return str_replace(array_keys($arabic), array_values($arabic), $text);
}

/**
 * Check if user is authenticated
 */
function auth() {
    return Auth::user();
}

/**
 * Check if user has permission
 */
function can($permission) {
    return Auth::hasPermission($permission);
}

/**
 * Check if user has role
 */
function hasRole($role) {
    return Auth::hasRole($role);
}

/**
 * Get current tenant ID
 */
function tenantId() {
    return Auth::tenantId();
}

/**
 * Format phone number
 */
function formatPhone($phone) {
    if (!$phone) return '';
    
    // Remove non-numeric characters
    $phone = preg_replace('/[^0-9]/', '', $phone);
    
    // Add country code if missing
    if (strlen($phone) === 9 && $phone[0] === '5') {
        $phone = '966' . $phone;
    } elseif (strlen($phone) === 10 && $phone[0] === '0') {
        $phone = '966' . substr($phone, 1);
    }
    
    // Format as +966 5X XXX XXXX
    if (strlen($phone) === 12 && substr($phone, 0, 3) === '966') {
        return '+966 ' . substr($phone, 3, 2) . ' ' . substr($phone, 5, 3) . ' ' . substr($phone, 8, 4);
    }
    
    return $phone;
}

/**
 * Generate QR code URL
 */
function qrCode($text, $size = 200) {
    return "https://api.qrserver.com/v1/create-qr-code/?size={$size}x{$size}&data=" . urlencode($text);
}

/**
 * Check if request is AJAX
 */
function isAjax() {
    return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
           strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
}

/**
 * Get client IP address
 */
function getClientIP() {
    $ipKeys = ['HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR'];
    
    foreach ($ipKeys as $key) {
        if (array_key_exists($key, $_SERVER) === true) {
            foreach (explode(',', $_SERVER[$key]) as $ip) {
                $ip = trim($ip);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                    return $ip;
                }
            }
        }
    }
    
    return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
}

/**
 * Get user agent
 */
function getUserAgent() {
    return $_SERVER['HTTP_USER_AGENT'] ?? '';
}

/**
 * Check if mobile device
 */
function isMobile() {
    return preg_match('/Mobile|Android|iPhone|iPad/', getUserAgent());
}

/**
 * Generate pagination links
 */
function paginate($currentPage, $totalPages, $url) {
    $links = [];
    $range = 2; // Number of links to show on each side of current page
    
    // Previous link
    if ($currentPage > 1) {
        $links[] = [
            'url' => $url . '?page=' . ($currentPage - 1),
            'text' => 'السابق',
            'active' => false
        ];
    }
    
    // Page links
    for ($i = max(1, $currentPage - $range); $i <= min($totalPages, $currentPage + $range); $i++) {
        $links[] = [
            'url' => $url . '?page=' . $i,
            'text' => $i,
            'active' => $i === $currentPage
        ];
    }
    
    // Next link
    if ($currentPage < $totalPages) {
        $links[] = [
            'url' => $url . '?page=' . ($currentPage + 1),
            'text' => 'التالي',
            'active' => false
        ];
    }
    
    return $links;
}
