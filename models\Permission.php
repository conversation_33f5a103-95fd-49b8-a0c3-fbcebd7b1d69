<?php
/**
 * Permission Model
 * Handles permissions and user authorization
 */

class Permission extends Model {
    protected $table = 'permissions';
    protected $fillable = ['name', 'display_name', 'description', 'module'];
    protected $timestamps = true;
    protected $softDeletes = false;
    protected $tenantColumn = null; // Permissions are global
    
    /**
     * Get all permissions grouped by module
     */
    public function getAllGrouped() {
        $sql = "SELECT * FROM {$this->table} ORDER BY module, display_name";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        $permissions = $stmt->fetchAll();
        
        $grouped = [];
        foreach ($permissions as $permission) {
            $grouped[$permission['module']][] = $permission;
        }
        
        return $grouped;
    }
    
    /**
     * Get permissions by module
     */
    public function getByModule($module) {
        $sql = "SELECT * FROM {$this->table} WHERE module = ? ORDER BY display_name";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$module]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get user permissions
     */
    public function getUserPermissions($userId) {
        $sql = "SELECT p.name 
                FROM {$this->table} p 
                INNER JOIN user_permissions up ON p.id = up.permission_id 
                WHERE up.user_id = ?";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$userId]);
        return $stmt->fetchAll(PDO::FETCH_COLUMN);
    }
    
    /**
     * Get user permissions with details
     */
    public function getUserPermissionsWithDetails($userId) {
        $sql = "SELECT p.*, up.created_at as granted_at, 
                       u.first_name as granted_by_first_name, 
                       u.last_name as granted_by_last_name
                FROM {$this->table} p 
                INNER JOIN user_permissions up ON p.id = up.permission_id 
                LEFT JOIN users u ON up.granted_by = u.id
                WHERE up.user_id = ?
                ORDER BY p.module, p.display_name";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$userId]);
        return $stmt->fetchAll();
    }
    
    /**
     * Check if user has permission
     */
    public function userHasPermission($userId, $permissionName) {
        $sql = "SELECT COUNT(*) as count 
                FROM {$this->table} p 
                INNER JOIN user_permissions up ON p.id = up.permission_id 
                WHERE up.user_id = ? AND p.name = ?";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$userId, $permissionName]);
        $result = $stmt->fetch();
        
        return $result['count'] > 0;
    }
    
    /**
     * Grant permission to user
     */
    public function grantToUser($userId, $permissionName, $grantedBy = null) {
        // Get permission ID
        $permission = $this->findByName($permissionName);
        if (!$permission) {
            return false;
        }
        
        $sql = "INSERT INTO user_permissions (user_id, permission_id, granted_by) 
                VALUES (?, ?, ?)
                ON DUPLICATE KEY UPDATE granted_by = VALUES(granted_by)";
        
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([$userId, $permission['id'], $grantedBy]);
    }
    
    /**
     * Revoke permission from user
     */
    public function revokeFromUser($userId, $permissionName) {
        $permission = $this->findByName($permissionName);
        if (!$permission) {
            return false;
        }
        
        $sql = "DELETE FROM user_permissions WHERE user_id = ? AND permission_id = ?";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([$userId, $permission['id']]);
    }
    
    /**
     * Grant multiple permissions to user
     */
    public function grantMultipleToUser($userId, $permissionNames, $grantedBy = null) {
        $this->db->beginTransaction();
        
        try {
            // First, remove all existing permissions
            $this->revokeAllFromUser($userId);
            
            // Then grant new permissions
            foreach ($permissionNames as $permissionName) {
                $this->grantToUser($userId, $permissionName, $grantedBy);
            }
            
            $this->db->commit();
            return true;
        } catch (Exception $e) {
            $this->db->rollback();
            return false;
        }
    }
    
    /**
     * Revoke all permissions from user
     */
    public function revokeAllFromUser($userId) {
        $sql = "DELETE FROM user_permissions WHERE user_id = ?";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([$userId]);
    }
    
    /**
     * Find permission by name
     */
    public function findByName($name) {
        $sql = "SELECT * FROM {$this->table} WHERE name = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$name]);
        return $stmt->fetch();
    }
    
    /**
     * Get available modules
     */
    public function getModules() {
        $sql = "SELECT DISTINCT module FROM {$this->table} ORDER BY module";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_COLUMN);
    }
    
    /**
     * Search permissions
     */
    public function search($query) {
        $sql = "SELECT * FROM {$this->table} 
                WHERE name LIKE ? OR display_name LIKE ? OR description LIKE ?
                ORDER BY module, display_name";
        
        $searchTerm = "%{$query}%";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$searchTerm, $searchTerm, $searchTerm]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get permission statistics
     */
    public function getStats() {
        $stats = [];
        
        // Total permissions
        $sql = "SELECT COUNT(*) as count FROM {$this->table}";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        $stats['total_permissions'] = $stmt->fetch()['count'];
        
        // Permissions by module
        $sql = "SELECT module, COUNT(*) as count FROM {$this->table} GROUP BY module ORDER BY module";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        $stats['permissions_by_module'] = $stmt->fetchAll();
        
        // Most granted permissions
        $sql = "SELECT p.name, p.display_name, COUNT(up.user_id) as users_count
                FROM {$this->table} p
                LEFT JOIN user_permissions up ON p.id = up.permission_id
                GROUP BY p.id
                ORDER BY users_count DESC
                LIMIT 10";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        $stats['most_granted'] = $stmt->fetchAll();
        
        return $stats;
    }
    
    /**
     * Get default permissions for role
     */
    public function getDefaultPermissionsForRole($role) {
        $defaultPermissions = [
            'super_admin' => [], // Super admin gets all permissions automatically
            'office_owner' => [
                'properties.view', 'properties.create', 'properties.edit', 'properties.delete', 'properties.export',
                'clients.view', 'clients.create', 'clients.edit', 'clients.delete', 'clients.export',
                'invoices.view', 'invoices.create', 'invoices.edit', 'invoices.delete', 'invoices.print',
                'reports.view', 'reports.export',
                'users.view', 'users.create', 'users.edit', 'users.delete',
                'settings.view', 'settings.edit'
            ],
            'employee' => [
                'properties.view', 'properties.create', 'properties.edit',
                'clients.view', 'clients.create', 'clients.edit',
                'invoices.view', 'invoices.create', 'invoices.edit',
                'reports.view'
            ]
        ];
        
        return $defaultPermissions[$role] ?? [];
    }
    
    /**
     * Assign default permissions to user based on role
     */
    public function assignDefaultPermissions($userId, $role, $grantedBy = null) {
        $permissions = $this->getDefaultPermissionsForRole($role);
        
        if (empty($permissions)) {
            return true;
        }
        
        return $this->grantMultipleToUser($userId, $permissions, $grantedBy);
    }
    
    /**
     * Check if permission name exists
     */
    public function nameExists($name, $excludeId = null) {
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE name = ?";
        $params = [$name];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        $result = $stmt->fetch();
        
        return $result['count'] > 0;
    }
    
    /**
     * Get users with specific permission
     */
    public function getUsersWithPermission($permissionName) {
        $sql = "SELECT u.id, u.first_name, u.last_name, u.email, u.role, t.name as tenant_name
                FROM users u
                INNER JOIN user_permissions up ON u.id = up.user_id
                INNER JOIN {$this->table} p ON up.permission_id = p.id
                LEFT JOIN tenants t ON u.tenant_id = t.id
                WHERE p.name = ? AND u.deleted_at IS NULL
                ORDER BY u.first_name, u.last_name";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$permissionName]);
        return $stmt->fetchAll();
    }
}
