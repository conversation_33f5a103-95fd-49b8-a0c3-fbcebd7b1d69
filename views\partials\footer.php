<footer class="footer mt-5 py-4 bg-white border-top">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-6">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0 me-3">
                        <i class="fas fa-building text-primary fa-2x"></i>
                    </div>
                    <div class="flex-grow-1">
                        <h6 class="mb-0"><?= APP_NAME ?></h6>
                        <small class="text-muted">نظام إدارة المكاتب العقارية المتكامل</small>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6 text-md-end">
                <div class="footer-links mb-2">
                    <a href="/help" class="text-decoration-none me-3">
                        <i class="fas fa-question-circle me-1"></i>
                        المساعدة
                    </a>
                    <a href="/support" class="text-decoration-none me-3">
                        <i class="fas fa-headset me-1"></i>
                        الدعم الفني
                    </a>
                    <a href="/privacy" class="text-decoration-none me-3">
                        <i class="fas fa-shield-alt me-1"></i>
                        الخصوصية
                    </a>
                    <a href="/terms" class="text-decoration-none">
                        <i class="fas fa-file-contract me-1"></i>
                        الشروط
                    </a>
                </div>
                
                <div class="footer-info">
                    <small class="text-muted">
                        © <?= date('Y') ?> <?= APP_NAME ?>. جميع الحقوق محفوظة.
                        <br>
                        الإصدار <?= APP_VERSION ?>
                    </small>
                </div>
            </div>
        </div>
        
        <!-- System Status (for Super Admin) -->
        <?php if (hasRole('super_admin')): ?>
            <div class="row mt-3 pt-3 border-top">
                <div class="col-12">
                    <div class="system-status">
                        <small class="text-muted">
                            <strong>حالة النظام:</strong>
                            
                            <!-- Database Status -->
                            <?php
                            try {
                                $db = Database::getInstance();
                                $dbStatus = $db->healthCheck();
                                $statusClass = $dbStatus ? 'text-success' : 'text-danger';
                                $statusIcon = $dbStatus ? 'fa-check-circle' : 'fa-exclamation-circle';
                                $statusText = $dbStatus ? 'متصل' : 'غير متصل';
                            } catch (Exception $e) {
                                $statusClass = 'text-danger';
                                $statusIcon = 'fa-exclamation-circle';
                                $statusText = 'خطأ';
                            }
                            ?>
                            <span class="me-3">
                                <i class="fas <?= $statusIcon ?> <?= $statusClass ?> me-1"></i>
                                قاعدة البيانات: <span class="<?= $statusClass ?>"><?= $statusText ?></span>
                            </span>
                            
                            <!-- Server Time -->
                            <span class="me-3">
                                <i class="fas fa-clock text-info me-1"></i>
                                الوقت: <?= arabicDate(date('Y-m-d H:i:s'), true) ?>
                            </span>
                            
                            <!-- Memory Usage -->
                            <?php
                            $memoryUsage = memory_get_usage(true);
                            $memoryLimit = ini_get('memory_limit');
                            $memoryUsageMB = round($memoryUsage / 1024 / 1024, 2);
                            ?>
                            <span class="me-3">
                                <i class="fas fa-memory text-warning me-1"></i>
                                الذاكرة: <?= $memoryUsageMB ?> MB
                            </span>
                            
                            <!-- Active Users -->
                            <?php
                            $userModel = new User();
                            $activeUsers = $userModel->query(
                                "SELECT COUNT(*) as count FROM users 
                                 WHERE last_login_at >= DATE_SUB(NOW(), INTERVAL 15 MINUTE) 
                                 AND deleted_at IS NULL"
                            )[0]['count'];
                            ?>
                            <span>
                                <i class="fas fa-users text-primary me-1"></i>
                                المستخدمين النشطين: <?= $activeUsers ?>
                            </span>
                        </small>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</footer>

<style>
.footer {
    margin-top: auto;
    background: white !important;
    border-top: 1px solid #e9ecef;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

.footer-links a {
    color: #6c757d;
    font-size: 0.875rem;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: var(--primary-color);
}

.footer-info {
    font-size: 0.75rem;
}

.system-status {
    font-size: 0.75rem;
    background: #f8f9fa;
    padding: 0.5rem;
    border-radius: 5px;
}

@media (max-width: 768px) {
    .footer .col-md-6 {
        text-align: center !important;
        margin-bottom: 1rem;
    }
    
    .footer-links {
        margin-bottom: 1rem !important;
    }
    
    .footer-links a {
        display: block;
        margin: 0.25rem 0;
    }
    
    .system-status {
        text-align: center;
    }
    
    .system-status span {
        display: block;
        margin: 0.25rem 0;
    }
}
</style>
