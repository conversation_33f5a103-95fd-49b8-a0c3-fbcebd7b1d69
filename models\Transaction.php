<?php
/**
 * Transaction Model
 * Handles financial transactions
 */

class Transaction extends Model {
    protected $table = 'transactions';
    protected $fillable = [
        'tenant_id', 'property_id', 'client_id', 'invoice_id', 'type', 'category',
        'amount', 'currency', 'description', 'reference_number', 'date',
        'payment_method', 'status', 'notes', 'created_by', 'updated_by'
    ];
    protected $timestamps = true;
    protected $softDeletes = true;
    
    // Transaction types
    const TYPE_INCOME = 'income';
    const TYPE_EXPENSE = 'expense';
    
    // Transaction categories
    const CATEGORY_COMMISSION = 'commission';
    const CATEGORY_RENT = 'rent';
    const CATEGORY_SALE = 'sale';
    const CATEGORY_MARKETING = 'marketing';
    const CATEGORY_OFFICE_EXPENSE = 'office_expense';
    const CATEGORY_OTHER = 'other';
    
    // Transaction statuses
    const STATUS_PENDING = 'pending';
    const STATUS_COMPLETED = 'completed';
    const STATUS_CANCELLED = 'cancelled';
    
    /**
     * Get transactions by type
     */
    public function getByType($type, $tenantId = null) {
        $tenantId = $tenantId ?: $this->getCurrentTenantId();
        
        $sql = "SELECT t.*, p.title as property_title, c.first_name, c.last_name,
                       i.invoice_number
                FROM {$this->table} t
                LEFT JOIN properties p ON t.property_id = p.id
                LEFT JOIN clients c ON t.client_id = c.id
                LEFT JOIN invoices i ON t.invoice_id = i.id
                WHERE t.type = ? AND t.tenant_id = ? AND t.deleted_at IS NULL
                ORDER BY t.date DESC, t.created_at DESC";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$type, $tenantId]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get transactions by category
     */
    public function getByCategory($category, $tenantId = null) {
        $tenantId = $tenantId ?: $this->getCurrentTenantId();
        
        $sql = "SELECT t.*, p.title as property_title, c.first_name, c.last_name,
                       i.invoice_number
                FROM {$this->table} t
                LEFT JOIN properties p ON t.property_id = p.id
                LEFT JOIN clients c ON t.client_id = c.id
                LEFT JOIN invoices i ON t.invoice_id = i.id
                WHERE t.category = ? AND t.tenant_id = ? AND t.deleted_at IS NULL
                ORDER BY t.date DESC, t.created_at DESC";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$category, $tenantId]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get transactions by date range
     */
    public function getByDateRange($startDate, $endDate, $tenantId = null) {
        $tenantId = $tenantId ?: $this->getCurrentTenantId();
        
        $sql = "SELECT t.*, p.title as property_title, c.first_name, c.last_name,
                       i.invoice_number
                FROM {$this->table} t
                LEFT JOIN properties p ON t.property_id = p.id
                LEFT JOIN clients c ON t.client_id = c.id
                LEFT JOIN invoices i ON t.invoice_id = i.id
                WHERE t.date BETWEEN ? AND ? AND t.tenant_id = ? AND t.deleted_at IS NULL
                ORDER BY t.date DESC, t.created_at DESC";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$startDate, $endDate, $tenantId]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get transaction statistics
     */
    public function getStats($tenantId = null, $period = 'month') {
        $tenantId = $tenantId ?: $this->getCurrentTenantId();
        
        $stats = [];
        
        // Date condition based on period
        $dateCondition = '';
        switch ($period) {
            case 'today':
                $dateCondition = "AND DATE(t.date) = CURDATE()";
                break;
            case 'week':
                $dateCondition = "AND t.date >= DATE_SUB(CURDATE(), INTERVAL 1 WEEK)";
                break;
            case 'month':
                $dateCondition = "AND MONTH(t.date) = MONTH(CURDATE()) AND YEAR(t.date) = YEAR(CURDATE())";
                break;
            case 'year':
                $dateCondition = "AND YEAR(t.date) = YEAR(CURDATE())";
                break;
        }
        
        // Total income
        $sql = "SELECT COALESCE(SUM(amount), 0) as total 
                FROM {$this->table} t
                WHERE t.type = 'income' AND t.tenant_id = ? AND t.deleted_at IS NULL {$dateCondition}";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$tenantId]);
        $stats['total_income'] = $stmt->fetch()['total'];
        
        // Total expenses
        $sql = "SELECT COALESCE(SUM(amount), 0) as total 
                FROM {$this->table} t
                WHERE t.type = 'expense' AND t.tenant_id = ? AND t.deleted_at IS NULL {$dateCondition}";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$tenantId]);
        $stats['total_expenses'] = $stmt->fetch()['total'];
        
        // Net profit
        $stats['net_profit'] = $stats['total_income'] - $stats['total_expenses'];
        
        // Transactions by category
        $sql = "SELECT category, type, SUM(amount) as total, COUNT(*) as count
                FROM {$this->table} t
                WHERE t.tenant_id = ? AND t.deleted_at IS NULL {$dateCondition}
                GROUP BY category, type
                ORDER BY total DESC";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$tenantId]);
        $stats['by_category'] = $stmt->fetchAll();
        
        // Monthly trends (last 12 months)
        $sql = "SELECT 
                    DATE_FORMAT(t.date, '%Y-%m') as month,
                    t.type,
                    SUM(t.amount) as total
                FROM {$this->table} t
                WHERE t.tenant_id = ? 
                AND t.date >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
                AND t.deleted_at IS NULL
                GROUP BY DATE_FORMAT(t.date, '%Y-%m'), t.type
                ORDER BY month";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$tenantId]);
        $stats['monthly_trends'] = $stmt->fetchAll();
        
        return $stats;
    }
    
    /**
     * Search transactions
     */
    public function search($query, $filters = [], $tenantId = null) {
        $tenantId = $tenantId ?: $this->getCurrentTenantId();
        
        $whereConditions = ["t.tenant_id = ?", "t.deleted_at IS NULL"];
        $params = [$tenantId];
        
        // Text search
        if ($query) {
            $whereConditions[] = "(t.description LIKE ? OR t.reference_number LIKE ? OR c.first_name LIKE ? OR c.last_name LIKE ? OR p.title LIKE ?)";
            $params[] = "%{$query}%";
            $params[] = "%{$query}%";
            $params[] = "%{$query}%";
            $params[] = "%{$query}%";
            $params[] = "%{$query}%";
        }
        
        // Filters
        if (isset($filters['type']) && $filters['type']) {
            $whereConditions[] = "t.type = ?";
            $params[] = $filters['type'];
        }
        
        if (isset($filters['category']) && $filters['category']) {
            $whereConditions[] = "t.category = ?";
            $params[] = $filters['category'];
        }
        
        if (isset($filters['status']) && $filters['status']) {
            $whereConditions[] = "t.status = ?";
            $params[] = $filters['status'];
        }
        
        if (isset($filters['date_from']) && $filters['date_from']) {
            $whereConditions[] = "t.date >= ?";
            $params[] = $filters['date_from'];
        }
        
        if (isset($filters['date_to']) && $filters['date_to']) {
            $whereConditions[] = "t.date <= ?";
            $params[] = $filters['date_to'];
        }
        
        if (isset($filters['amount_min']) && $filters['amount_min']) {
            $whereConditions[] = "t.amount >= ?";
            $params[] = $filters['amount_min'];
        }
        
        if (isset($filters['amount_max']) && $filters['amount_max']) {
            $whereConditions[] = "t.amount <= ?";
            $params[] = $filters['amount_max'];
        }
        
        $sql = "SELECT t.*, p.title as property_title, c.first_name, c.last_name,
                       i.invoice_number, u.first_name as created_by_first_name, u.last_name as created_by_last_name
                FROM {$this->table} t
                LEFT JOIN properties p ON t.property_id = p.id
                LEFT JOIN clients c ON t.client_id = c.id
                LEFT JOIN invoices i ON t.invoice_id = i.id
                LEFT JOIN users u ON t.created_by = u.id
                WHERE " . implode(' AND ', $whereConditions) . "
                ORDER BY t.date DESC, t.created_at DESC";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
    }
    
    /**
     * Get transactions with pagination
     */
    public function getPaginated($page = 1, $perPage = 20, $filters = [], $tenantId = null) {
        $tenantId = $tenantId ?: $this->getCurrentTenantId();
        $offset = ($page - 1) * $perPage;
        
        $whereConditions = ["t.tenant_id = ?", "t.deleted_at IS NULL"];
        $params = [$tenantId];
        
        // Apply filters
        if (isset($filters['search']) && $filters['search']) {
            $whereConditions[] = "(t.description LIKE ? OR t.reference_number LIKE ?)";
            $params[] = "%{$filters['search']}%";
            $params[] = "%{$filters['search']}%";
        }
        
        if (isset($filters['type']) && $filters['type']) {
            $whereConditions[] = "t.type = ?";
            $params[] = $filters['type'];
        }
        
        if (isset($filters['category']) && $filters['category']) {
            $whereConditions[] = "t.category = ?";
            $params[] = $filters['category'];
        }
        
        $whereClause = implode(' AND ', $whereConditions);
        
        // Get total count
        $countSql = "SELECT COUNT(*) as total 
                     FROM {$this->table} t
                     WHERE {$whereClause}";
        $stmt = $this->db->prepare($countSql);
        $stmt->execute($params);
        $total = $stmt->fetch()['total'];
        
        // Get data
        $sql = "SELECT t.*, p.title as property_title, c.first_name, c.last_name,
                       i.invoice_number
                FROM {$this->table} t
                LEFT JOIN properties p ON t.property_id = p.id
                LEFT JOIN clients c ON t.client_id = c.id
                LEFT JOIN invoices i ON t.invoice_id = i.id
                WHERE {$whereClause}
                ORDER BY t.date DESC, t.created_at DESC
                LIMIT {$offset}, {$perPage}";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        $data = $stmt->fetchAll();
        
        return [
            'data' => $data,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total' => $total,
                'total_pages' => ceil($total / $perPage),
                'has_prev' => $page > 1,
                'has_next' => $page < ceil($total / $perPage)
            ]
        ];
    }
    
    /**
     * Get transaction with details
     */
    public function getWithDetails($transactionId) {
        $sql = "SELECT t.*, 
                       p.title as property_title, p.address as property_address,
                       c.first_name as client_first_name, c.last_name as client_last_name,
                       c.email as client_email, c.phone as client_phone,
                       i.invoice_number, i.total_amount as invoice_amount,
                       u1.first_name as created_by_first_name, u1.last_name as created_by_last_name,
                       u2.first_name as updated_by_first_name, u2.last_name as updated_by_last_name
                FROM {$this->table} t
                LEFT JOIN properties p ON t.property_id = p.id
                LEFT JOIN clients c ON t.client_id = c.id
                LEFT JOIN invoices i ON t.invoice_id = i.id
                LEFT JOIN users u1 ON t.created_by = u1.id
                LEFT JOIN users u2 ON t.updated_by = u2.id
                WHERE t.id = ? AND t.deleted_at IS NULL";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$transactionId]);
        return $stmt->fetch();
    }
    
    /**
     * Get category display name
     */
    public function getCategoryDisplayName($category) {
        $categories = [
            'commission' => 'عمولة',
            'rent' => 'إيجار',
            'sale' => 'بيع',
            'marketing' => 'تسويق',
            'office_expense' => 'مصاريف المكتب',
            'other' => 'أخرى'
        ];
        
        return $categories[$category] ?? $category;
    }
    
    /**
     * Get type display name
     */
    public function getTypeDisplayName($type) {
        $types = [
            'income' => 'دخل',
            'expense' => 'مصروف'
        ];
        
        return $types[$type] ?? $type;
    }
    
    /**
     * Get status display name
     */
    public function getStatusDisplayName($status) {
        $statuses = [
            'pending' => 'معلق',
            'completed' => 'مكتمل',
            'cancelled' => 'ملغي'
        ];
        
        return $statuses[$status] ?? $status;
    }
}
