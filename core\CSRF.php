<?php
/**
 * CSRF Protection Class
 * Handles Cross-Site Request Forgery protection
 */

class CSRF {
    
    /**
     * Generate CSRF token
     */
    public static function generateToken() {
        $token = Session::getCSRFToken();
        
        if (!$token) {
            $token = bin2hex(random_bytes(32));
            Session::setCSRFToken($token);
        }
        
        return $token;
    }
    
    /**
     * Verify CSRF token
     */
    public static function verifyToken($token) {
        if (!$token) {
            return false;
        }
        
        $sessionToken = Session::getCSRFToken();
        
        if (!$sessionToken) {
            return false;
        }
        
        return hash_equals($sessionToken, $token);
    }
    
    /**
     * Check CSRF token from request
     */
    public static function check() {
        $token = self::getTokenFromRequest();
        return self::verifyToken($token);
    }
    
    /**
     * Get CSRF token from request
     */
    private static function getTokenFromRequest() {
        // Check POST data
        if (isset($_POST[CSRF_TOKEN_NAME])) {
            return $_POST[CSRF_TOKEN_NAME];
        }
        
        // Check headers
        $headers = getallheaders();
        
        if (isset($headers['X-CSRF-Token'])) {
            return $headers['X-CSRF-Token'];
        }
        
        if (isset($headers['X-XSRF-Token'])) {
            return $headers['X-XSRF-Token'];
        }
        
        return null;
    }
    
    /**
     * Validate CSRF token for form submissions
     */
    public static function validateForm() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            if (!self::check()) {
                throw new Exception('رمز الحماية غير صحيح. يرجى إعادة المحاولة.');
            }
        }
    }
    
    /**
     * Generate CSRF meta tag for HTML head
     */
    public static function metaTag() {
        $token = self::generateToken();
        return "<meta name=\"csrf-token\" content=\"{$token}\">";
    }
    
    /**
     * Generate CSRF hidden input field
     */
    public static function field() {
        $token = self::generateToken();
        return "<input type=\"hidden\" name=\"" . CSRF_TOKEN_NAME . "\" value=\"{$token}\">";
    }
    
    /**
     * Get current CSRF token
     */
    public static function token() {
        return self::generateToken();
    }
    
    /**
     * Regenerate CSRF token
     */
    public static function regenerate() {
        $token = bin2hex(random_bytes(32));
        Session::setCSRFToken($token);
        return $token;
    }
    
    /**
     * Clear CSRF token
     */
    public static function clear() {
        Session::remove('csrf_token');
    }
}
