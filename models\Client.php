<?php
/**
 * Client Model
 * Handles client data and operations
 */

class Client extends Model {
    protected $table = 'clients';
    protected $fillable = [
        'tenant_id', 'first_name', 'last_name', 'email', 'phone', 'phone_secondary',
        'national_id', 'type', 'budget_min', 'budget_max', 'preferred_areas',
        'preferred_types', 'address', 'city', 'notes', 'source', 'status',
        'assigned_to', 'created_by', 'updated_by'
    ];
    protected $timestamps = true;
    protected $softDeletes = true;
    
    /**
     * Get clients by type
     */
    public function getByType($type, $tenantId = null) {
        $tenantId = $tenantId ?: $this->getCurrentTenantId();
        
        $sql = "SELECT * FROM {$this->table} 
                WHERE type = ? AND tenant_id = ? AND deleted_at IS NULL 
                ORDER BY created_at DESC";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$type, $tenantId]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get clients by status
     */
    public function getByStatus($status, $tenantId = null) {
        $tenantId = $tenantId ?: $this->getCurrentTenantId();
        
        $sql = "SELECT * FROM {$this->table} 
                WHERE status = ? AND tenant_id = ? AND deleted_at IS NULL 
                ORDER BY created_at DESC";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$status, $tenantId]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get clients assigned to user
     */
    public function getAssignedTo($userId, $tenantId = null) {
        $tenantId = $tenantId ?: $this->getCurrentTenantId();
        
        $sql = "SELECT * FROM {$this->table} 
                WHERE assigned_to = ? AND tenant_id = ? AND deleted_at IS NULL 
                ORDER BY created_at DESC";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$userId, $tenantId]);
        return $stmt->fetchAll();
    }
    
    /**
     * Search clients
     */
    public function search($query, $filters = [], $tenantId = null) {
        $tenantId = $tenantId ?: $this->getCurrentTenantId();
        
        $whereConditions = ["tenant_id = ?", "deleted_at IS NULL"];
        $params = [$tenantId];
        
        // Text search
        if ($query) {
            $whereConditions[] = "(first_name LIKE ? OR last_name LIKE ? OR email LIKE ? OR phone LIKE ? OR national_id LIKE ?)";
            $params[] = "%{$query}%";
            $params[] = "%{$query}%";
            $params[] = "%{$query}%";
            $params[] = "%{$query}%";
            $params[] = "%{$query}%";
        }
        
        // Filters
        if (isset($filters['type']) && $filters['type']) {
            $whereConditions[] = "type = ?";
            $params[] = $filters['type'];
        }
        
        if (isset($filters['status']) && $filters['status']) {
            $whereConditions[] = "status = ?";
            $params[] = $filters['status'];
        }
        
        if (isset($filters['assigned_to']) && $filters['assigned_to']) {
            $whereConditions[] = "assigned_to = ?";
            $params[] = $filters['assigned_to'];
        }
        
        if (isset($filters['city']) && $filters['city']) {
            $whereConditions[] = "city = ?";
            $params[] = $filters['city'];
        }
        
        $sql = "SELECT * FROM {$this->table} 
                WHERE " . implode(' AND ', $whereConditions) . " 
                ORDER BY created_at DESC";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
    }
    
    /**
     * Get client statistics
     */
    public function getStats($tenantId = null) {
        $tenantId = $tenantId ?: $this->getCurrentTenantId();
        
        $stats = [];
        
        // Total clients
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE tenant_id = ? AND deleted_at IS NULL";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$tenantId]);
        $stats['total'] = $stmt->fetch()['count'];
        
        // Clients by status
        $sql = "SELECT status, COUNT(*) as count 
                FROM {$this->table} 
                WHERE tenant_id = ? AND deleted_at IS NULL 
                GROUP BY status";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$tenantId]);
        $stats['by_status'] = $stmt->fetchAll();
        
        // Clients by type
        $sql = "SELECT type, COUNT(*) as count 
                FROM {$this->table} 
                WHERE tenant_id = ? AND deleted_at IS NULL 
                GROUP BY type";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$tenantId]);
        $stats['by_type'] = $stmt->fetchAll();
        
        // New clients this month
        $sql = "SELECT COUNT(*) as count 
                FROM {$this->table} 
                WHERE tenant_id = ? 
                AND MONTH(created_at) = MONTH(CURRENT_DATE()) 
                AND YEAR(created_at) = YEAR(CURRENT_DATE())
                AND deleted_at IS NULL";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$tenantId]);
        $stats['new_this_month'] = $stmt->fetch()['count'];
        
        return $stats;
    }
    
    /**
     * Get client with interactions
     */
    public function getWithInteractions($clientId) {
        $client = $this->find($clientId);
        
        if (!$client) {
            return null;
        }
        
        // Get interactions
        $sql = "SELECT ci.*, p.title as property_title, u.first_name, u.last_name
                FROM client_interactions ci
                LEFT JOIN properties p ON ci.property_id = p.id
                LEFT JOIN users u ON ci.created_by = u.id
                WHERE ci.client_id = ?
                ORDER BY ci.date DESC";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$clientId]);
        $client['interactions'] = $stmt->fetchAll();
        
        return $client;
    }
    
    /**
     * Get client full name
     */
    public function getFullName($client) {
        return trim($client['first_name'] . ' ' . $client['last_name']);
    }
    
    /**
     * Check if phone exists
     */
    public function phoneExists($phone, $excludeId = null, $tenantId = null) {
        $tenantId = $tenantId ?: $this->getCurrentTenantId();
        
        $sql = "SELECT COUNT(*) as count FROM {$this->table} 
                WHERE phone = ? AND tenant_id = ? AND deleted_at IS NULL";
        $params = [$phone, $tenantId];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        $result = $stmt->fetch();
        
        return $result['count'] > 0;
    }
    
    /**
     * Check if email exists
     */
    public function emailExists($email, $excludeId = null, $tenantId = null) {
        if (!$email) return false;
        
        $tenantId = $tenantId ?: $this->getCurrentTenantId();
        
        $sql = "SELECT COUNT(*) as count FROM {$this->table} 
                WHERE email = ? AND tenant_id = ? AND deleted_at IS NULL";
        $params = [$email, $tenantId];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        $result = $stmt->fetch();
        
        return $result['count'] > 0;
    }
    
    /**
     * Get matching properties for client
     */
    public function getMatchingProperties($clientId) {
        $client = $this->find($clientId);
        
        if (!$client) {
            return [];
        }
        
        $whereConditions = ["tenant_id = ?", "deleted_at IS NULL", "status = 'متاح'"];
        $params = [$client['tenant_id']];
        
        // Budget filter
        if ($client['budget_min'] || $client['budget_max']) {
            if ($client['budget_min']) {
                $whereConditions[] = "price >= ?";
                $params[] = $client['budget_min'];
            }
            if ($client['budget_max']) {
                $whereConditions[] = "price <= ?";
                $params[] = $client['budget_max'];
            }
        }
        
        // Preferred types
        if ($client['preferred_types']) {
            $types = json_decode($client['preferred_types'], true);
            if (!empty($types)) {
                $placeholders = str_repeat('?,', count($types) - 1) . '?';
                $whereConditions[] = "type IN ({$placeholders})";
                $params = array_merge($params, $types);
            }
        }
        
        // Preferred areas
        if ($client['preferred_areas']) {
            $areas = json_decode($client['preferred_areas'], true);
            if (!empty($areas)) {
                $areaConditions = [];
                foreach ($areas as $area) {
                    $areaConditions[] = "city LIKE ? OR district LIKE ? OR address LIKE ?";
                    $params[] = "%{$area}%";
                    $params[] = "%{$area}%";
                    $params[] = "%{$area}%";
                }
                $whereConditions[] = "(" . implode(' OR ', $areaConditions) . ")";
            }
        }
        
        $sql = "SELECT * FROM properties 
                WHERE " . implode(' AND ', $whereConditions) . " 
                ORDER BY created_at DESC 
                LIMIT 20";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
    }
    
    /**
     * Get clients with pagination and filters
     */
    public function getPaginated($page = 1, $perPage = 20, $filters = [], $tenantId = null) {
        $tenantId = $tenantId ?: $this->getCurrentTenantId();
        $offset = ($page - 1) * $perPage;
        
        $whereConditions = ["tenant_id = ?", "deleted_at IS NULL"];
        $params = [$tenantId];
        
        // Apply filters
        if (isset($filters['search']) && $filters['search']) {
            $whereConditions[] = "(first_name LIKE ? OR last_name LIKE ? OR email LIKE ? OR phone LIKE ?)";
            $params[] = "%{$filters['search']}%";
            $params[] = "%{$filters['search']}%";
            $params[] = "%{$filters['search']}%";
            $params[] = "%{$filters['search']}%";
        }
        
        if (isset($filters['type']) && $filters['type']) {
            $whereConditions[] = "type = ?";
            $params[] = $filters['type'];
        }
        
        if (isset($filters['status']) && $filters['status']) {
            $whereConditions[] = "status = ?";
            $params[] = $filters['status'];
        }
        
        $whereClause = implode(' AND ', $whereConditions);
        
        // Get total count
        $countSql = "SELECT COUNT(*) as total FROM {$this->table} WHERE {$whereClause}";
        $stmt = $this->db->prepare($countSql);
        $stmt->execute($params);
        $total = $stmt->fetch()['total'];
        
        // Get data
        $sql = "SELECT c.*, u.first_name as assigned_first_name, u.last_name as assigned_last_name
                FROM {$this->table} c
                LEFT JOIN users u ON c.assigned_to = u.id
                WHERE {$whereClause} 
                ORDER BY c.created_at DESC 
                LIMIT {$offset}, {$perPage}";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        $data = $stmt->fetchAll();
        
        return [
            'data' => $data,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total' => $total,
                'total_pages' => ceil($total / $perPage),
                'has_prev' => $page > 1,
                'has_next' => $page < ceil($total / $perPage)
            ]
        ];
    }
    
    /**
     * Get recent clients
     */
    public function getRecent($limit = 10, $tenantId = null) {
        $tenantId = $tenantId ?: $this->getCurrentTenantId();
        
        $sql = "SELECT * FROM {$this->table} 
                WHERE tenant_id = ? AND deleted_at IS NULL 
                ORDER BY created_at DESC 
                LIMIT ?";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$tenantId, $limit]);
        return $stmt->fetchAll();
    }
}
