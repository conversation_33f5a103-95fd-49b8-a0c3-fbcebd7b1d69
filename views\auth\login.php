<div class="row justify-content-center">
    <div class="col-md-6">
        <h2 class="text-center mb-4">تسجيل الدخول</h2>
        
        <form method="POST" action="/login" id="loginForm">
            <?= csrf_field() ?>
            
            <div class="mb-3">
                <label for="email" class="form-label">البريد الإلكتروني</label>
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-envelope"></i>
                    </span>
                    <input type="email" 
                           class="form-control <?= $this->hasError('email') ? 'is-invalid' : '' ?>" 
                           id="email" 
                           name="email" 
                           value="<?= $this->old('email') ?>"
                           placeholder="أدخل بريدك الإلكتروني"
                           required>
                </div>
                <?php if ($this->hasError('email')): ?>
                    <div class="invalid-feedback d-block">
                        <?= $this->error('email') ?>
                    </div>
                <?php endif; ?>
            </div>
            
            <div class="mb-3">
                <label for="password" class="form-label">كلمة المرور</label>
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-lock"></i>
                    </span>
                    <input type="password" 
                           class="form-control <?= $this->hasError('password') ? 'is-invalid' : '' ?>" 
                           id="password" 
                           name="password" 
                           placeholder="أدخل كلمة المرور"
                           required>
                    <button type="button" class="btn btn-outline-secondary" id="togglePassword">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
                <?php if ($this->hasError('password')): ?>
                    <div class="invalid-feedback d-block">
                        <?= $this->error('password') ?>
                    </div>
                <?php endif; ?>
            </div>
            
            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="remember" name="remember" value="1">
                        <label class="form-check-label" for="remember">
                            تذكرني
                        </label>
                    </div>
                </div>
                <div class="col-md-6 text-end">
                    <a href="/forgot-password" class="text-decoration-none">
                        نسيت كلمة المرور؟
                    </a>
                </div>
            </div>
            
            <div class="d-grid mb-3">
                <button type="submit" class="btn btn-primary btn-lg">
                    <i class="fas fa-sign-in-alt me-2"></i>
                    تسجيل الدخول
                </button>
            </div>
        </form>
        
        <div class="auth-links">
            <p class="mb-0">
                ليس لديك حساب؟ 
                <a href="/register">إنشاء حساب جديد</a>
            </p>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Toggle password visibility
    $('#togglePassword').on('click', function() {
        const passwordField = $('#password');
        const icon = $(this).find('i');
        
        if (passwordField.attr('type') === 'password') {
            passwordField.attr('type', 'text');
            icon.removeClass('fa-eye').addClass('fa-eye-slash');
        } else {
            passwordField.attr('type', 'password');
            icon.removeClass('fa-eye-slash').addClass('fa-eye');
        }
    });
    
    // Form validation
    $('#loginForm').on('submit', function(e) {
        let isValid = true;
        
        // Email validation
        const email = $('#email').val();
        if (!email || !isValidEmail(email)) {
            showFieldError('email', 'يرجى إدخال بريد إلكتروني صحيح');
            isValid = false;
        } else {
            clearFieldError('email');
        }
        
        // Password validation
        const password = $('#password').val();
        if (!password || password.length < 6) {
            showFieldError('password', 'كلمة المرور يجب أن تكون 6 أحرف على الأقل');
            isValid = false;
        } else {
            clearFieldError('password');
        }
        
        if (!isValid) {
            e.preventDefault();
        }
    });
    
    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    
    function showFieldError(fieldName, message) {
        const field = $('#' + fieldName);
        field.addClass('is-invalid');
        
        // Remove existing error message
        field.closest('.mb-3').find('.invalid-feedback').remove();
        
        // Add new error message
        field.closest('.input-group').after('<div class="invalid-feedback d-block">' + message + '</div>');
    }
    
    function clearFieldError(fieldName) {
        const field = $('#' + fieldName);
        field.removeClass('is-invalid');
        field.closest('.mb-3').find('.invalid-feedback').remove();
    }
});
</script>
