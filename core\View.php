<?php
/**
 * View Class
 * Handles template rendering and view management
 */

class View {
    private $variables = [];
    private $layout = 'layouts/main';
    
    public function __construct() {
        // Set default variables
        $this->variables = [
            'title' => APP_NAME,
            'description' => 'نظام إدارة المكاتب العقارية المتكامل',
            'keywords' => 'عقارات، إدارة، مكاتب، نظام، سعودية',
            'lang' => 'ar',
            'dir' => 'rtl'
        ];
    }
    
    public function set($key, $value) {
        $this->variables[$key] = $value;
    }
    
    public function get($key, $default = null) {
        return $this->variables[$key] ?? $default;
    }
    
    public function setLayout($layout) {
        $this->layout = $layout;
    }
    
    public function render($template, $variables = []) {
        // Merge variables
        $this->variables = array_merge($this->variables, $variables);
        
        // Extract variables for use in template
        extract($this->variables);
        
        // Start output buffering
        ob_start();
        
        // Include the template
        $templatePath = VIEWS_PATH . '/' . $template . '.php';
        if (!file_exists($templatePath)) {
            throw new Exception("Template not found: {$templatePath}");
        }
        
        include $templatePath;
        
        // Get template content
        $content = ob_get_clean();
        
        // If no layout is specified, return content directly
        if (!$this->layout) {
            echo $content;
            return;
        }
        
        // Set content variable for layout
        $this->set('content', $content);
        
        // Render layout
        $layoutPath = VIEWS_PATH . '/' . $this->layout . '.php';
        if (!file_exists($layoutPath)) {
            throw new Exception("Layout not found: {$layoutPath}");
        }
        
        // Extract variables again for layout
        extract($this->variables);
        
        include $layoutPath;
    }
    
    public function renderPartial($partial, $variables = []) {
        // Merge variables
        $mergedVariables = array_merge($this->variables, $variables);
        
        // Extract variables for use in partial
        extract($mergedVariables);
        
        // Include the partial
        $partialPath = VIEWS_PATH . '/partials/' . $partial . '.php';
        if (!file_exists($partialPath)) {
            throw new Exception("Partial not found: {$partialPath}");
        }
        
        include $partialPath;
    }
    
    public function include($file, $variables = []) {
        // Merge variables
        $mergedVariables = array_merge($this->variables, $variables);
        
        // Extract variables for use in file
        extract($mergedVariables);
        
        // Include the file
        $filePath = VIEWS_PATH . '/' . $file . '.php';
        if (!file_exists($filePath)) {
            throw new Exception("Include file not found: {$filePath}");
        }
        
        include $filePath;
    }
    
    public function exists($template) {
        $templatePath = VIEWS_PATH . '/' . $template . '.php';
        return file_exists($templatePath);
    }
    
    // Helper methods for use in templates
    
    public function escape($string) {
        return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
    }
    
    public function url($path = '') {
        return rtrim(APP_URL, '/') . '/' . ltrim($path, '/');
    }
    
    public function asset($path) {
        return $this->url('assets/' . ltrim($path, '/'));
    }
    
    public function upload($path) {
        return $this->url('uploads/' . ltrim($path, '/'));
    }
    
    public function formatDate($date, $format = 'Y-m-d') {
        if (!$date) return '';
        
        $timestamp = is_string($date) ? strtotime($date) : $date;
        return date($format, $timestamp);
    }
    
    public function formatArabicDate($date, $includeTime = false) {
        if (!$date) return '';
        
        $timestamp = is_string($date) ? strtotime($date) : $date;
        $day = date('j', $timestamp);
        $month = GREGORIAN_MONTHS[date('n', $timestamp)];
        $year = date('Y', $timestamp);
        
        $formatted = "{$day} {$month} {$year}";
        
        if ($includeTime) {
            $time = date('H:i', $timestamp);
            $formatted .= " الساعة {$time}";
        }
        
        return $formatted;
    }
    
    public function formatCurrency($amount, $currency = null) {
        $currency = $currency ?: DEFAULT_CURRENCY;
        $symbol = CURRENCY_SYMBOL;
        
        // Format number with Arabic numerals
        $formatted = number_format($amount, 2, '.', '٬');
        
        return "{$formatted} {$symbol}";
    }
    
    public function formatNumber($number) {
        // Convert to Arabic numerals
        $arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
        $englishNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
        
        $formatted = number_format($number, 0, '.', '٬');
        return str_replace($englishNumbers, $arabicNumbers, $formatted);
    }
    
    public function truncate($text, $length = 100, $suffix = '...') {
        if (mb_strlen($text, 'UTF-8') <= $length) {
            return $text;
        }
        
        return mb_substr($text, 0, $length, 'UTF-8') . $suffix;
    }
    
    public function timeAgo($date) {
        if (!$date) return '';
        
        $timestamp = is_string($date) ? strtotime($date) : $date;
        $diff = time() - $timestamp;
        
        if ($diff < 60) {
            return 'منذ لحظات';
        } elseif ($diff < 3600) {
            $minutes = floor($diff / 60);
            return "منذ {$minutes} دقيقة";
        } elseif ($diff < 86400) {
            $hours = floor($diff / 3600);
            return "منذ {$hours} ساعة";
        } elseif ($diff < 2592000) {
            $days = floor($diff / 86400);
            return "منذ {$days} يوم";
        } else {
            return $this->formatArabicDate($date);
        }
    }
    
    public function csrfField() {
        $token = CSRF::generateToken();
        return "<input type=\"hidden\" name=\"" . CSRF_TOKEN_NAME . "\" value=\"{$token}\">";
    }
    
    public function methodField($method) {
        return "<input type=\"hidden\" name=\"_method\" value=\"{$method}\">";
    }
    
    public function flashMessage($type = null) {
        if ($type) {
            return Session::getFlash($type);
        }
        
        $messages = [];
        $types = ['success', 'error', 'warning', 'info'];
        
        foreach ($types as $messageType) {
            $message = Session::getFlash($messageType);
            if ($message) {
                $messages[$messageType] = $message;
            }
        }
        
        return $messages;
    }
    
    public function hasFlash($type = null) {
        if ($type) {
            return Session::hasFlash($type);
        }
        
        $types = ['success', 'error', 'warning', 'info'];
        foreach ($types as $messageType) {
            if (Session::hasFlash($messageType)) {
                return true;
            }
        }
        
        return false;
    }
    
    public function old($key, $default = '') {
        return Session::getOldInput($key, $default);
    }
    
    public function errors($key = null) {
        if ($key) {
            return Session::getError($key);
        }
        
        return Session::getErrors();
    }
    
    public function hasError($key) {
        return Session::hasError($key);
    }
    
    public function selected($value, $selected, $default = '') {
        return $value == $selected ? 'selected' : $default;
    }
    
    public function checked($value, $checked, $default = '') {
        return $value == $checked ? 'checked' : $default;
    }
    
    public function active($path, $class = 'active') {
        $currentPath = $_SERVER['REQUEST_URI'];
        return strpos($currentPath, $path) === 0 ? $class : '';
    }
}
