<?php
/**
 * Validation Class
 * Handles form validation with Arabic error messages
 */

class Validator {
    private $errors = [];
    private $data = [];
    private $rules = [];
    
    /**
     * Validate data against rules
     */
    public function validate($data, $rules) {
        $this->data = $data;
        $this->rules = $rules;
        $this->errors = [];
        
        foreach ($rules as $field => $fieldRules) {
            $this->validateField($field, $fieldRules);
        }
        
        return empty($this->errors);
    }
    
    /**
     * Validate individual field
     */
    private function validateField($field, $rules) {
        $value = $this->data[$field] ?? null;
        $rulesArray = is_string($rules) ? explode('|', $rules) : $rules;
        
        foreach ($rulesArray as $rule) {
            $this->applyRule($field, $value, $rule);
        }
    }
    
    /**
     * Apply validation rule
     */
    private function applyRule($field, $value, $rule) {
        // Parse rule and parameters
        $ruleParts = explode(':', $rule);
        $ruleName = $ruleParts[0];
        $parameters = isset($ruleParts[1]) ? explode(',', $ruleParts[1]) : [];
        
        switch ($ruleName) {
            case 'required':
                $this->validateRequired($field, $value);
                break;
                
            case 'email':
                $this->validateEmail($field, $value);
                break;
                
            case 'min':
                $this->validateMin($field, $value, $parameters[0]);
                break;
                
            case 'max':
                $this->validateMax($field, $value, $parameters[0]);
                break;
                
            case 'min_length':
                $this->validateMinLength($field, $value, $parameters[0]);
                break;
                
            case 'max_length':
                $this->validateMaxLength($field, $value, $parameters[0]);
                break;
                
            case 'numeric':
                $this->validateNumeric($field, $value);
                break;
                
            case 'integer':
                $this->validateInteger($field, $value);
                break;
                
            case 'phone':
                $this->validatePhone($field, $value);
                break;
                
            case 'unique':
                $this->validateUnique($field, $value, $parameters);
                break;
                
            case 'exists':
                $this->validateExists($field, $value, $parameters);
                break;
                
            case 'confirmed':
                $this->validateConfirmed($field, $value);
                break;
                
            case 'in':
                $this->validateIn($field, $value, $parameters);
                break;
                
            case 'not_in':
                $this->validateNotIn($field, $value, $parameters);
                break;
                
            case 'regex':
                $this->validateRegex($field, $value, $parameters[0]);
                break;
                
            case 'date':
                $this->validateDate($field, $value);
                break;
                
            case 'url':
                $this->validateUrl($field, $value);
                break;
                
            case 'file':
                $this->validateFile($field, $value);
                break;
                
            case 'image':
                $this->validateImage($field, $value);
                break;
                
            case 'mimes':
                $this->validateMimes($field, $value, $parameters);
                break;
                
            case 'max_file_size':
                $this->validateMaxFileSize($field, $value, $parameters[0]);
                break;
        }
    }
    
    /**
     * Validation methods
     */
    private function validateRequired($field, $value) {
        if (is_null($value) || $value === '' || (is_array($value) && empty($value))) {
            $this->addError($field, ERROR_MESSAGES['required']);
        }
    }
    
    private function validateEmail($field, $value) {
        if ($value && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
            $this->addError($field, ERROR_MESSAGES['email']);
        }
    }
    
    private function validateMin($field, $value, $min) {
        if ($value !== null && $value < $min) {
            $this->addError($field, "يجب أن تكون القيمة أكبر من أو تساوي {$min}");
        }
    }
    
    private function validateMax($field, $value, $max) {
        if ($value !== null && $value > $max) {
            $this->addError($field, "يجب أن تكون القيمة أقل من أو تساوي {$max}");
        }
    }
    
    private function validateMinLength($field, $value, $minLength) {
        if ($value && mb_strlen($value, 'UTF-8') < $minLength) {
            $this->addError($field, sprintf(ERROR_MESSAGES['min_length'], $minLength));
        }
    }
    
    private function validateMaxLength($field, $value, $maxLength) {
        if ($value && mb_strlen($value, 'UTF-8') > $maxLength) {
            $this->addError($field, sprintf(ERROR_MESSAGES['max_length'], $maxLength));
        }
    }
    
    private function validateNumeric($field, $value) {
        if ($value !== null && !is_numeric($value)) {
            $this->addError($field, ERROR_MESSAGES['numeric']);
        }
    }
    
    private function validateInteger($field, $value) {
        if ($value !== null && !filter_var($value, FILTER_VALIDATE_INT)) {
            $this->addError($field, 'يجب أن يكون رقماً صحيحاً');
        }
    }
    
    private function validatePhone($field, $value) {
        if ($value && !preg_match('/^(\+966|0)?[5][0-9]{8}$/', $value)) {
            $this->addError($field, ERROR_MESSAGES['phone']);
        }
    }
    
    private function validateUnique($field, $value, $parameters) {
        if (!$value) return;
        
        $table = $parameters[0];
        $column = $parameters[1] ?? $field;
        $except = $parameters[2] ?? null;
        
        $db = Database::getInstance();
        $sql = "SELECT COUNT(*) as count FROM {$table} WHERE {$column} = ?";
        $params = [$value];
        
        if ($except) {
            $sql .= " AND id != ?";
            $params[] = $except;
        }
        
        $result = $db->execute($sql, $params)->fetch();
        
        if ($result['count'] > 0) {
            $this->addError($field, ERROR_MESSAGES['unique']);
        }
    }
    
    private function validateExists($field, $value, $parameters) {
        if (!$value) return;
        
        $table = $parameters[0];
        $column = $parameters[1] ?? $field;
        
        $db = Database::getInstance();
        $sql = "SELECT COUNT(*) as count FROM {$table} WHERE {$column} = ?";
        $result = $db->execute($sql, [$value])->fetch();
        
        if ($result['count'] == 0) {
            $this->addError($field, 'القيمة المحددة غير موجودة');
        }
    }
    
    private function validateConfirmed($field, $value) {
        $confirmationField = $field . '_confirmation';
        $confirmationValue = $this->data[$confirmationField] ?? null;
        
        if ($value !== $confirmationValue) {
            $this->addError($field, 'التأكيد غير متطابق');
        }
    }
    
    private function validateIn($field, $value, $parameters) {
        if ($value && !in_array($value, $parameters)) {
            $this->addError($field, 'القيمة المحددة غير صحيحة');
        }
    }
    
    private function validateNotIn($field, $value, $parameters) {
        if ($value && in_array($value, $parameters)) {
            $this->addError($field, 'القيمة المحددة غير مسموحة');
        }
    }
    
    private function validateRegex($field, $value, $pattern) {
        if ($value && !preg_match($pattern, $value)) {
            $this->addError($field, 'تنسيق القيمة غير صحيح');
        }
    }
    
    private function validateDate($field, $value) {
        if ($value && !strtotime($value)) {
            $this->addError($field, 'تاريخ غير صحيح');
        }
    }
    
    private function validateUrl($field, $value) {
        if ($value && !filter_var($value, FILTER_VALIDATE_URL)) {
            $this->addError($field, 'رابط غير صحيح');
        }
    }
    
    private function validateFile($field, $value) {
        if (isset($_FILES[$field]) && $_FILES[$field]['error'] !== UPLOAD_ERR_OK) {
            $this->addError($field, 'فشل في رفع الملف');
        }
    }
    
    private function validateImage($field, $value) {
        if (isset($_FILES[$field])) {
            $file = $_FILES[$field];
            if ($file['error'] === UPLOAD_ERR_OK) {
                $imageInfo = getimagesize($file['tmp_name']);
                if (!$imageInfo) {
                    $this->addError($field, 'الملف ليس صورة صحيحة');
                }
            }
        }
    }
    
    private function validateMimes($field, $value, $allowedTypes) {
        if (isset($_FILES[$field])) {
            $file = $_FILES[$field];
            if ($file['error'] === UPLOAD_ERR_OK) {
                $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
                if (!in_array($extension, $allowedTypes)) {
                    $this->addError($field, ERROR_MESSAGES['file_type']);
                }
            }
        }
    }
    
    private function validateMaxFileSize($field, $value, $maxSize) {
        if (isset($_FILES[$field])) {
            $file = $_FILES[$field];
            if ($file['error'] === UPLOAD_ERR_OK && $file['size'] > $maxSize) {
                $this->addError($field, ERROR_MESSAGES['file_size']);
            }
        }
    }
    
    /**
     * Add validation error
     */
    private function addError($field, $message) {
        $this->errors[$field] = $message;
    }
    
    /**
     * Get validation errors
     */
    public function getErrors() {
        return $this->errors;
    }
    
    /**
     * Get first error for field
     */
    public function getError($field) {
        return $this->errors[$field] ?? null;
    }
    
    /**
     * Check if field has error
     */
    public function hasError($field) {
        return isset($this->errors[$field]);
    }
    
    /**
     * Check if validation failed
     */
    public function fails() {
        return !empty($this->errors);
    }
    
    /**
     * Check if validation passed
     */
    public function passes() {
        return empty($this->errors);
    }
}
