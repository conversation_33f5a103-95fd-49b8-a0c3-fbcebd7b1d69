-- Real Estate SaaS Database Schema
-- Multi-tenant architecture with complete data isolation
-- Character set: UTF8MB4 for full Arabic support

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- Create database
CREATE DATABASE IF NOT EXISTS `real_estate_saas` 
DEFAULT CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE `real_estate_saas`;

-- =============================================
-- TENANTS (OFFICES) TABLE
-- =============================================
CREATE TABLE `tenants` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL COMMENT 'اسم المكتب',
  `slug` varchar(255) NOT NULL UNIQUE COMMENT 'معرف المكتب',
  `email` varchar(255) NOT NULL COMMENT 'البريد الإلكتروني',
  `phone` varchar(20) DEFAULT NULL COMMENT 'رقم الهاتف',
  `address` text DEFAULT NULL COMMENT 'العنوان',
  `city` varchar(100) DEFAULT NULL COMMENT 'المدينة',
  `country` varchar(100) DEFAULT 'Saudi Arabia' COMMENT 'الدولة',
  `logo` varchar(255) DEFAULT NULL COMMENT 'شعار المكتب',
  `website` varchar(255) DEFAULT NULL COMMENT 'الموقع الإلكتروني',
  `tax_number` varchar(50) DEFAULT NULL COMMENT 'الرقم الضريبي',
  `commercial_register` varchar(50) DEFAULT NULL COMMENT 'السجل التجاري',
  `settings` json DEFAULT NULL COMMENT 'إعدادات المكتب',
  `status` enum('active','inactive','suspended') DEFAULT 'active' COMMENT 'حالة المكتب',
  `subscription_expires_at` datetime DEFAULT NULL COMMENT 'تاريخ انتهاء الاشتراك',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`),
  KEY `status` (`status`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- USERS TABLE
-- =============================================
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `tenant_id` int(11) DEFAULT NULL COMMENT 'معرف المكتب',
  `first_name` varchar(100) NOT NULL COMMENT 'الاسم الأول',
  `last_name` varchar(100) NOT NULL COMMENT 'الاسم الأخير',
  `email` varchar(255) NOT NULL COMMENT 'البريد الإلكتروني',
  `phone` varchar(20) DEFAULT NULL COMMENT 'رقم الهاتف',
  `password` varchar(255) NOT NULL COMMENT 'كلمة المرور',
  `role` enum('super_admin','office_owner','employee') NOT NULL DEFAULT 'employee' COMMENT 'الدور',
  `avatar` varchar(255) DEFAULT NULL COMMENT 'الصورة الشخصية',
  `status` enum('active','inactive','suspended') DEFAULT 'active' COMMENT 'الحالة',
  `email_verified_at` timestamp NULL DEFAULT NULL COMMENT 'تاريخ تأكيد البريد',
  `last_login_at` timestamp NULL DEFAULT NULL COMMENT 'آخر تسجيل دخول',
  `remember_token` varchar(100) DEFAULT NULL COMMENT 'رمز التذكر',
  `settings` json DEFAULT NULL COMMENT 'إعدادات المستخدم',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`),
  KEY `tenant_id` (`tenant_id`),
  KEY `role` (`role`),
  KEY `status` (`status`),
  CONSTRAINT `users_tenant_fk` FOREIGN KEY (`tenant_id`) REFERENCES `tenants` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- PERMISSIONS TABLE
-- =============================================
CREATE TABLE `permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT 'اسم الصلاحية',
  `display_name` varchar(255) NOT NULL COMMENT 'الاسم المعروض',
  `description` text DEFAULT NULL COMMENT 'الوصف',
  `module` varchar(50) NOT NULL COMMENT 'الوحدة',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`),
  KEY `module` (`module`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- USER PERMISSIONS TABLE
-- =============================================
CREATE TABLE `user_permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `permission_id` int(11) NOT NULL,
  `granted_by` int(11) DEFAULT NULL COMMENT 'من منح الصلاحية',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_permission` (`user_id`, `permission_id`),
  KEY `permission_id` (`permission_id`),
  KEY `granted_by` (`granted_by`),
  CONSTRAINT `user_permissions_user_fk` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `user_permissions_permission_fk` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE,
  CONSTRAINT `user_permissions_granted_by_fk` FOREIGN KEY (`granted_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- PROPERTIES TABLE
-- =============================================
CREATE TABLE `properties` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `tenant_id` int(11) NOT NULL COMMENT 'معرف المكتب',
  `title` varchar(255) NOT NULL COMMENT 'عنوان العقار',
  `description` text DEFAULT NULL COMMENT 'وصف العقار',
  `type` enum('apartment','villa','office','shop','warehouse','land','building','farm') NOT NULL COMMENT 'نوع العقار',
  `purpose` enum('sale','rent','both') NOT NULL DEFAULT 'sale' COMMENT 'الغرض',
  `status` enum('متاح','مباع','مؤجر','قيد التفاوض','محجوز') DEFAULT 'متاح' COMMENT 'حالة العقار',
  `price` decimal(15,2) NOT NULL COMMENT 'السعر',
  `area` decimal(10,2) DEFAULT NULL COMMENT 'المساحة بالمتر المربع',
  `bedrooms` int(3) DEFAULT NULL COMMENT 'عدد غرف النوم',
  `bathrooms` int(3) DEFAULT NULL COMMENT 'عدد دورات المياه',
  `floor` int(3) DEFAULT NULL COMMENT 'الطابق',
  `parking` int(3) DEFAULT NULL COMMENT 'عدد مواقف السيارات',
  `furnished` enum('yes','no','partial') DEFAULT 'no' COMMENT 'مفروش',
  `address` text NOT NULL COMMENT 'العنوان',
  `city` varchar(100) NOT NULL COMMENT 'المدينة',
  `district` varchar(100) DEFAULT NULL COMMENT 'الحي',
  `latitude` decimal(10,8) DEFAULT NULL COMMENT 'خط العرض',
  `longitude` decimal(11,8) DEFAULT NULL COMMENT 'خط الطول',
  `features` json DEFAULT NULL COMMENT 'المميزات',
  `images` json DEFAULT NULL COMMENT 'الصور',
  `documents` json DEFAULT NULL COMMENT 'المستندات',
  `owner_name` varchar(255) DEFAULT NULL COMMENT 'اسم المالك',
  `owner_phone` varchar(20) DEFAULT NULL COMMENT 'هاتف المالك',
  `owner_email` varchar(255) DEFAULT NULL COMMENT 'بريد المالك',
  `commission_rate` decimal(5,2) DEFAULT NULL COMMENT 'نسبة العمولة',
  `commission_amount` decimal(15,2) DEFAULT NULL COMMENT 'مبلغ العمولة',
  `views_count` int(11) DEFAULT 0 COMMENT 'عدد المشاهدات',
  `is_featured` tinyint(1) DEFAULT 0 COMMENT 'عقار مميز',
  `created_by` int(11) NOT NULL COMMENT 'أنشئ بواسطة',
  `updated_by` int(11) DEFAULT NULL COMMENT 'حدث بواسطة',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `tenant_id` (`tenant_id`),
  KEY `type` (`type`),
  KEY `purpose` (`purpose`),
  KEY `status` (`status`),
  KEY `price` (`price`),
  KEY `city` (`city`),
  KEY `created_by` (`created_by`),
  KEY `created_at` (`created_at`),
  FULLTEXT KEY `search` (`title`, `description`, `address`),
  CONSTRAINT `properties_tenant_fk` FOREIGN KEY (`tenant_id`) REFERENCES `tenants` (`id`) ON DELETE CASCADE,
  CONSTRAINT `properties_created_by_fk` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `properties_updated_by_fk` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- CLIENTS TABLE
-- =============================================
CREATE TABLE `clients` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `tenant_id` int(11) NOT NULL COMMENT 'معرف المكتب',
  `first_name` varchar(100) NOT NULL COMMENT 'الاسم الأول',
  `last_name` varchar(100) NOT NULL COMMENT 'الاسم الأخير',
  `email` varchar(255) DEFAULT NULL COMMENT 'البريد الإلكتروني',
  `phone` varchar(20) NOT NULL COMMENT 'رقم الهاتف',
  `phone_secondary` varchar(20) DEFAULT NULL COMMENT 'رقم هاتف ثانوي',
  `national_id` varchar(20) DEFAULT NULL COMMENT 'رقم الهوية',
  `type` enum('buyer','seller','tenant','landlord','investor') NOT NULL COMMENT 'نوع العميل',
  `budget_min` decimal(15,2) DEFAULT NULL COMMENT 'الحد الأدنى للميزانية',
  `budget_max` decimal(15,2) DEFAULT NULL COMMENT 'الحد الأقصى للميزانية',
  `preferred_areas` json DEFAULT NULL COMMENT 'المناطق المفضلة',
  `preferred_types` json DEFAULT NULL COMMENT 'أنواع العقارات المفضلة',
  `address` text DEFAULT NULL COMMENT 'العنوان',
  `city` varchar(100) DEFAULT NULL COMMENT 'المدينة',
  `notes` text DEFAULT NULL COMMENT 'ملاحظات',
  `source` varchar(100) DEFAULT NULL COMMENT 'مصدر العميل',
  `status` enum('active','inactive','converted') DEFAULT 'active' COMMENT 'حالة العميل',
  `assigned_to` int(11) DEFAULT NULL COMMENT 'مسؤول العميل',
  `created_by` int(11) NOT NULL COMMENT 'أنشئ بواسطة',
  `updated_by` int(11) DEFAULT NULL COMMENT 'حدث بواسطة',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `tenant_id` (`tenant_id`),
  KEY `type` (`type`),
  KEY `status` (`status`),
  KEY `assigned_to` (`assigned_to`),
  KEY `created_by` (`created_by`),
  KEY `phone` (`phone`),
  FULLTEXT KEY `search` (`first_name`, `last_name`, `email`, `phone`),
  CONSTRAINT `clients_tenant_fk` FOREIGN KEY (`tenant_id`) REFERENCES `tenants` (`id`) ON DELETE CASCADE,
  CONSTRAINT `clients_assigned_to_fk` FOREIGN KEY (`assigned_to`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `clients_created_by_fk` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `clients_updated_by_fk` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- CLIENT INTERACTIONS TABLE
-- =============================================
CREATE TABLE `client_interactions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `tenant_id` int(11) NOT NULL COMMENT 'معرف المكتب',
  `client_id` int(11) NOT NULL COMMENT 'معرف العميل',
  `property_id` int(11) DEFAULT NULL COMMENT 'معرف العقار',
  `type` enum('call','email','meeting','viewing','offer','contract') NOT NULL COMMENT 'نوع التفاعل',
  `subject` varchar(255) DEFAULT NULL COMMENT 'الموضوع',
  `description` text DEFAULT NULL COMMENT 'الوصف',
  `date` datetime NOT NULL COMMENT 'تاريخ التفاعل',
  `duration` int(11) DEFAULT NULL COMMENT 'المدة بالدقائق',
  `outcome` enum('positive','negative','neutral','follow_up') DEFAULT NULL COMMENT 'النتيجة',
  `next_action` text DEFAULT NULL COMMENT 'الإجراء التالي',
  `next_action_date` datetime DEFAULT NULL COMMENT 'تاريخ الإجراء التالي',
  `attachments` json DEFAULT NULL COMMENT 'المرفقات',
  `created_by` int(11) NOT NULL COMMENT 'أنشئ بواسطة',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `tenant_id` (`tenant_id`),
  KEY `client_id` (`client_id`),
  KEY `property_id` (`property_id`),
  KEY `type` (`type`),
  KEY `date` (`date`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `client_interactions_tenant_fk` FOREIGN KEY (`tenant_id`) REFERENCES `tenants` (`id`) ON DELETE CASCADE,
  CONSTRAINT `client_interactions_client_fk` FOREIGN KEY (`client_id`) REFERENCES `clients` (`id`) ON DELETE CASCADE,
  CONSTRAINT `client_interactions_property_fk` FOREIGN KEY (`property_id`) REFERENCES `properties` (`id`) ON DELETE SET NULL,
  CONSTRAINT `client_interactions_created_by_fk` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- INVOICES TABLE
-- =============================================
CREATE TABLE `invoices` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `tenant_id` int(11) NOT NULL COMMENT 'معرف المكتب',
  `invoice_number` varchar(50) NOT NULL COMMENT 'رقم الفاتورة',
  `client_id` int(11) DEFAULT NULL COMMENT 'معرف العميل',
  `property_id` int(11) DEFAULT NULL COMMENT 'معرف العقار',
  `type` enum('commission','service','rental','other') NOT NULL DEFAULT 'commission' COMMENT 'نوع الفاتورة',
  `status` enum('مسودة','مرسلة','مدفوعة','متأخرة','ملغية') DEFAULT 'مسودة' COMMENT 'حالة الفاتورة',
  `issue_date` date NOT NULL COMMENT 'تاريخ الإصدار',
  `due_date` date DEFAULT NULL COMMENT 'تاريخ الاستحقاق',
  `paid_date` date DEFAULT NULL COMMENT 'تاريخ الدفع',
  `subtotal` decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT 'المجموع الفرعي',
  `tax_rate` decimal(5,2) DEFAULT 15.00 COMMENT 'نسبة الضريبة',
  `tax_amount` decimal(15,2) DEFAULT 0.00 COMMENT 'مبلغ الضريبة',
  `discount_amount` decimal(15,2) DEFAULT 0.00 COMMENT 'مبلغ الخصم',
  `total_amount` decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT 'المبلغ الإجمالي',
  `paid_amount` decimal(15,2) DEFAULT 0.00 COMMENT 'المبلغ المدفوع',
  `currency` varchar(3) DEFAULT 'SAR' COMMENT 'العملة',
  `payment_method` enum('cash','bank_transfer','check','credit_card','installments') DEFAULT NULL COMMENT 'طريقة الدفع',
  `payment_reference` varchar(100) DEFAULT NULL COMMENT 'مرجع الدفع',
  `notes` text DEFAULT NULL COMMENT 'ملاحظات',
  `items` json DEFAULT NULL COMMENT 'بنود الفاتورة',
  `created_by` int(11) NOT NULL COMMENT 'أنشئ بواسطة',
  `updated_by` int(11) DEFAULT NULL COMMENT 'حدث بواسطة',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `tenant_invoice_number` (`tenant_id`, `invoice_number`),
  KEY `client_id` (`client_id`),
  KEY `property_id` (`property_id`),
  KEY `status` (`status`),
  KEY `issue_date` (`issue_date`),
  KEY `due_date` (`due_date`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `invoices_tenant_fk` FOREIGN KEY (`tenant_id`) REFERENCES `tenants` (`id`) ON DELETE CASCADE,
  CONSTRAINT `invoices_client_fk` FOREIGN KEY (`client_id`) REFERENCES `clients` (`id`) ON DELETE SET NULL,
  CONSTRAINT `invoices_property_fk` FOREIGN KEY (`property_id`) REFERENCES `properties` (`id`) ON DELETE SET NULL,
  CONSTRAINT `invoices_created_by_fk` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `invoices_updated_by_fk` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- TRANSACTIONS TABLE
-- =============================================
CREATE TABLE `transactions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `tenant_id` int(11) NOT NULL COMMENT 'معرف المكتب',
  `property_id` int(11) NOT NULL COMMENT 'معرف العقار',
  `client_id` int(11) NOT NULL COMMENT 'معرف العميل',
  `invoice_id` int(11) DEFAULT NULL COMMENT 'معرف الفاتورة',
  `type` enum('sale','rent','commission') NOT NULL COMMENT 'نوع المعاملة',
  `amount` decimal(15,2) NOT NULL COMMENT 'المبلغ',
  `commission_rate` decimal(5,2) DEFAULT NULL COMMENT 'نسبة العمولة',
  `commission_amount` decimal(15,2) DEFAULT NULL COMMENT 'مبلغ العمولة',
  `date` date NOT NULL COMMENT 'تاريخ المعاملة',
  `status` enum('pending','completed','cancelled') DEFAULT 'pending' COMMENT 'حالة المعاملة',
  `notes` text DEFAULT NULL COMMENT 'ملاحظات',
  `documents` json DEFAULT NULL COMMENT 'المستندات',
  `created_by` int(11) NOT NULL COMMENT 'أنشئ بواسطة',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `tenant_id` (`tenant_id`),
  KEY `property_id` (`property_id`),
  KEY `client_id` (`client_id`),
  KEY `invoice_id` (`invoice_id`),
  KEY `type` (`type`),
  KEY `date` (`date`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `transactions_tenant_fk` FOREIGN KEY (`tenant_id`) REFERENCES `tenants` (`id`) ON DELETE CASCADE,
  CONSTRAINT `transactions_property_fk` FOREIGN KEY (`property_id`) REFERENCES `properties` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `transactions_client_fk` FOREIGN KEY (`client_id`) REFERENCES `clients` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `transactions_invoice_fk` FOREIGN KEY (`invoice_id`) REFERENCES `invoices` (`id`) ON DELETE SET NULL,
  CONSTRAINT `transactions_created_by_fk` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- ACTIVITY LOGS TABLE
-- =============================================
CREATE TABLE `activity_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `tenant_id` int(11) DEFAULT NULL COMMENT 'معرف المكتب',
  `user_id` int(11) DEFAULT NULL COMMENT 'معرف المستخدم',
  `action` varchar(100) NOT NULL COMMENT 'الإجراء',
  `model` varchar(100) DEFAULT NULL COMMENT 'النموذج',
  `model_id` int(11) DEFAULT NULL COMMENT 'معرف النموذج',
  `description` text DEFAULT NULL COMMENT 'الوصف',
  `old_values` json DEFAULT NULL COMMENT 'القيم القديمة',
  `new_values` json DEFAULT NULL COMMENT 'القيم الجديدة',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'عنوان IP',
  `user_agent` text DEFAULT NULL COMMENT 'معلومات المتصفح',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `tenant_id` (`tenant_id`),
  KEY `user_id` (`user_id`),
  KEY `action` (`action`),
  KEY `model` (`model`),
  KEY `created_at` (`created_at`),
  CONSTRAINT `activity_logs_tenant_fk` FOREIGN KEY (`tenant_id`) REFERENCES `tenants` (`id`) ON DELETE CASCADE,
  CONSTRAINT `activity_logs_user_fk` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- SETTINGS TABLE
-- =============================================
CREATE TABLE `settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `tenant_id` int(11) DEFAULT NULL COMMENT 'معرف المكتب (NULL للإعدادات العامة)',
  `key` varchar(100) NOT NULL COMMENT 'مفتاح الإعداد',
  `value` text DEFAULT NULL COMMENT 'قيمة الإعداد',
  `type` enum('string','integer','boolean','json') DEFAULT 'string' COMMENT 'نوع البيانات',
  `description` text DEFAULT NULL COMMENT 'الوصف',
  `is_public` tinyint(1) DEFAULT 0 COMMENT 'إعداد عام',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `tenant_key` (`tenant_id`, `key`),
  KEY `tenant_id` (`tenant_id`),
  KEY `key` (`key`),
  CONSTRAINT `settings_tenant_fk` FOREIGN KEY (`tenant_id`) REFERENCES `tenants` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- NOTIFICATIONS TABLE
-- =============================================
CREATE TABLE `notifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `tenant_id` int(11) DEFAULT NULL COMMENT 'معرف المكتب',
  `user_id` int(11) NOT NULL COMMENT 'معرف المستخدم',
  `type` varchar(100) NOT NULL COMMENT 'نوع الإشعار',
  `title` varchar(255) NOT NULL COMMENT 'عنوان الإشعار',
  `message` text NOT NULL COMMENT 'رسالة الإشعار',
  `data` json DEFAULT NULL COMMENT 'بيانات إضافية',
  `read_at` timestamp NULL DEFAULT NULL COMMENT 'تاريخ القراءة',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `tenant_id` (`tenant_id`),
  KEY `user_id` (`user_id`),
  KEY `type` (`type`),
  KEY `read_at` (`read_at`),
  KEY `created_at` (`created_at`),
  CONSTRAINT `notifications_tenant_fk` FOREIGN KEY (`tenant_id`) REFERENCES `tenants` (`id`) ON DELETE CASCADE,
  CONSTRAINT `notifications_user_fk` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- INSERT DEFAULT PERMISSIONS
-- =============================================
INSERT INTO `permissions` (`name`, `display_name`, `description`, `module`) VALUES
-- Properties permissions
('properties.view', 'عرض العقارات', 'عرض قائمة العقارات وتفاصيلها', 'properties'),
('properties.create', 'إضافة عقار', 'إضافة عقار جديد', 'properties'),
('properties.edit', 'تعديل عقار', 'تعديل بيانات العقار', 'properties'),
('properties.delete', 'حذف عقار', 'حذف العقار', 'properties'),
('properties.export', 'تصدير العقارات', 'تصدير قائمة العقارات', 'properties'),

-- Clients permissions
('clients.view', 'عرض العملاء', 'عرض قائمة العملاء وتفاصيلهم', 'clients'),
('clients.create', 'إضافة عميل', 'إضافة عميل جديد', 'clients'),
('clients.edit', 'تعديل عميل', 'تعديل بيانات العميل', 'clients'),
('clients.delete', 'حذف عميل', 'حذف العميل', 'clients'),
('clients.export', 'تصدير العملاء', 'تصدير قائمة العملاء', 'clients'),

-- Invoices permissions
('invoices.view', 'عرض الفواتير', 'عرض قائمة الفواتير وتفاصيلها', 'invoices'),
('invoices.create', 'إنشاء فاتورة', 'إنشاء فاتورة جديدة', 'invoices'),
('invoices.edit', 'تعديل فاتورة', 'تعديل بيانات الفاتورة', 'invoices'),
('invoices.delete', 'حذف فاتورة', 'حذف الفاتورة', 'invoices'),
('invoices.print', 'طباعة فاتورة', 'طباعة الفاتورة', 'invoices'),

-- Reports permissions
('reports.view', 'عرض التقارير', 'عرض التقارير والإحصائيات', 'reports'),
('reports.export', 'تصدير التقارير', 'تصدير التقارير', 'reports'),

-- Users permissions
('users.view', 'عرض المستخدمين', 'عرض قائمة المستخدمين', 'users'),
('users.create', 'إضافة مستخدم', 'إضافة مستخدم جديد', 'users'),
('users.edit', 'تعديل مستخدم', 'تعديل بيانات المستخدم', 'users'),
('users.delete', 'حذف مستخدم', 'حذف المستخدم', 'users'),

-- Settings permissions
('settings.view', 'عرض الإعدادات', 'عرض إعدادات النظام', 'settings'),
('settings.edit', 'تعديل الإعدادات', 'تعديل إعدادات النظام', 'settings');

-- =============================================
-- INSERT DEFAULT SUPER ADMIN USER
-- =============================================
INSERT INTO `users` (`first_name`, `last_name`, `email`, `password`, `role`, `status`, `email_verified_at`) VALUES
('مدير', 'النظام', '<EMAIL>', '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'super_admin', 'active', NOW());

-- =============================================
-- CREATE INDEXES FOR PERFORMANCE
-- =============================================
CREATE INDEX idx_properties_search ON properties(tenant_id, status, type, city, price);
CREATE INDEX idx_clients_search ON clients(tenant_id, status, type, phone);
CREATE INDEX idx_invoices_search ON invoices(tenant_id, status, issue_date, due_date);
CREATE INDEX idx_transactions_search ON transactions(tenant_id, type, date, status);
CREATE INDEX idx_activity_logs_search ON activity_logs(tenant_id, user_id, action, created_at);

SET FOREIGN_KEY_CHECKS = 1;
