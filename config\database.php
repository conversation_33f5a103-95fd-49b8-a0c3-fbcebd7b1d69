<?php
/**
 * Database Configuration and Connection Manager
 * Handles database connections with multi-tenant support
 */

class Database {
    private static $instance = null;
    private $connection = null;
    
    private function __construct() {
        $this->connect();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function connect() {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . DB_CHARSET . " COLLATE " . DB_COLLATION,
                PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true,
                PDO::ATTR_PERSISTENT => false
            ];
            
            $this->connection = new PDO($dsn, DB_USER, DB_PASS, $options);
            
            // Set timezone
            $this->connection->exec("SET time_zone = '+03:00'");
            
        } catch (PDOException $e) {
            error_log("Database connection failed: " . $e->getMessage());
            throw new Exception("فشل في الاتصال بقاعدة البيانات");
        }
    }
    
    public function getConnection() {
        return $this->connection;
    }
    
    public function prepare($sql) {
        return $this->connection->prepare($sql);
    }
    
    public function execute($sql, $params = []) {
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            error_log("Database query failed: " . $e->getMessage() . " SQL: " . $sql);
            throw new Exception("خطأ في تنفيذ الاستعلام");
        }
    }
    
    public function query($sql) {
        try {
            return $this->connection->query($sql);
        } catch (PDOException $e) {
            error_log("Database query failed: " . $e->getMessage() . " SQL: " . $sql);
            throw new Exception("خطأ في تنفيذ الاستعلام");
        }
    }
    
    public function lastInsertId() {
        return $this->connection->lastInsertId();
    }
    
    public function beginTransaction() {
        return $this->connection->beginTransaction();
    }
    
    public function commit() {
        return $this->connection->commit();
    }
    
    public function rollback() {
        return $this->connection->rollback();
    }
    
    public function inTransaction() {
        return $this->connection->inTransaction();
    }
    
    /**
     * Get database statistics
     */
    public function getStats() {
        $stats = [];
        
        try {
            // Get table sizes
            $sql = "SELECT 
                        table_name as 'table_name',
                        round(((data_length + index_length) / 1024 / 1024), 2) as 'size_mb',
                        table_rows as 'rows'
                    FROM information_schema.TABLES 
                    WHERE table_schema = :database
                    ORDER BY (data_length + index_length) DESC";
            
            $stmt = $this->prepare($sql);
            $stmt->execute(['database' => DB_NAME]);
            $stats['tables'] = $stmt->fetchAll();
            
            // Get total database size
            $sql = "SELECT 
                        round(sum(data_length + index_length) / 1024 / 1024, 2) as 'total_size_mb'
                    FROM information_schema.TABLES 
                    WHERE table_schema = :database";
            
            $stmt = $this->prepare($sql);
            $stmt->execute(['database' => DB_NAME]);
            $stats['total_size'] = $stmt->fetch()['total_size_mb'];
            
        } catch (Exception $e) {
            error_log("Failed to get database stats: " . $e->getMessage());
            $stats = ['error' => 'فشل في جلب إحصائيات قاعدة البيانات'];
        }
        
        return $stats;
    }
    
    /**
     * Check database health
     */
    public function healthCheck() {
        try {
            $this->connection->query("SELECT 1");
            return true;
        } catch (Exception $e) {
            error_log("Database health check failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Optimize database tables
     */
    public function optimizeTables() {
        try {
            $sql = "SELECT table_name FROM information_schema.TABLES WHERE table_schema = :database";
            $stmt = $this->prepare($sql);
            $stmt->execute(['database' => DB_NAME]);
            $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            foreach ($tables as $table) {
                $this->connection->exec("OPTIMIZE TABLE `{$table}`");
            }
            
            return true;
        } catch (Exception $e) {
            error_log("Database optimization failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Create database backup
     */
    public function backup($filename = null) {
        if (!$filename) {
            $filename = 'backup_' . date('Y-m-d_H-i-s') . '.sql';
        }
        
        $backupPath = ROOT_PATH . '/backups/' . $filename;
        
        // Create backups directory if it doesn't exist
        if (!is_dir(dirname($backupPath))) {
            mkdir(dirname($backupPath), 0755, true);
        }
        
        try {
            $command = sprintf(
                'mysqldump --user=%s --password=%s --host=%s %s > %s',
                escapeshellarg(DB_USER),
                escapeshellarg(DB_PASS),
                escapeshellarg(DB_HOST),
                escapeshellarg(DB_NAME),
                escapeshellarg($backupPath)
            );
            
            exec($command, $output, $returnCode);
            
            if ($returnCode === 0) {
                return $backupPath;
            } else {
                throw new Exception("Backup command failed with return code: " . $returnCode);
            }
            
        } catch (Exception $e) {
            error_log("Database backup failed: " . $e->getMessage());
            return false;
        }
    }
    
    public function __destruct() {
        $this->connection = null;
    }
}

// Initialize database connection
try {
    $db = Database::getInstance();
} catch (Exception $e) {
    error_log("Failed to initialize database: " . $e->getMessage());
    die("خطأ في الاتصال بقاعدة البيانات");
}
