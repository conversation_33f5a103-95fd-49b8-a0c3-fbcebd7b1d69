<?php
/**
 * Base Model Class
 * All models extend from this base class
 */

abstract class Model {
    protected $db;
    protected $table;
    protected $primaryKey = 'id';
    protected $fillable = [];
    protected $hidden = [];
    protected $timestamps = true;
    protected $softDeletes = false;
    protected $tenantColumn = 'tenant_id';
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Find record by ID
     */
    public function find($id) {
        $sql = "SELECT * FROM {$this->table} WHERE {$this->primaryKey} = ?";
        
        if ($this->softDeletes) {
            $sql .= " AND deleted_at IS NULL";
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$id]);
        
        return $stmt->fetch();
    }
    
    /**
     * Find record by ID with tenant check
     */
    public function findWithTenant($id, $tenantId = null) {
        $tenantId = $tenantId ?: $this->getCurrentTenantId();
        
        $sql = "SELECT * FROM {$this->table} WHERE {$this->primaryKey} = ? AND {$this->tenantColumn} = ?";
        
        if ($this->softDeletes) {
            $sql .= " AND deleted_at IS NULL";
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$id, $tenantId]);
        
        return $stmt->fetch();
    }
    
    /**
     * Get all records
     */
    public function all($tenantId = null) {
        $sql = "SELECT * FROM {$this->table}";
        $params = [];
        
        if ($this->hasTenantColumn() && $tenantId !== false) {
            $tenantId = $tenantId ?: $this->getCurrentTenantId();
            $sql .= " WHERE {$this->tenantColumn} = ?";
            $params[] = $tenantId;
        }
        
        if ($this->softDeletes) {
            $sql .= $this->hasTenantColumn() && $tenantId !== false ? " AND" : " WHERE";
            $sql .= " deleted_at IS NULL";
        }
        
        $sql .= " ORDER BY created_at DESC";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        
        return $stmt->fetchAll();
    }
    
    /**
     * Create new record
     */
    public function create($data) {
        // Filter fillable fields
        $data = $this->filterFillable($data);
        
        // Add tenant ID if applicable
        if ($this->hasTenantColumn() && !isset($data[$this->tenantColumn])) {
            $data[$this->tenantColumn] = $this->getCurrentTenantId();
        }
        
        // Add timestamps
        if ($this->timestamps) {
            $data['created_at'] = date('Y-m-d H:i:s');
            $data['updated_at'] = date('Y-m-d H:i:s');
        }
        
        $fields = array_keys($data);
        $placeholders = array_fill(0, count($fields), '?');
        
        $sql = "INSERT INTO {$this->table} (" . implode(', ', $fields) . ") VALUES (" . implode(', ', $placeholders) . ")";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute(array_values($data));
        
        return $this->db->lastInsertId();
    }
    
    /**
     * Update record
     */
    public function update($id, $data) {
        // Filter fillable fields
        $data = $this->filterFillable($data);
        
        // Add updated timestamp
        if ($this->timestamps) {
            $data['updated_at'] = date('Y-m-d H:i:s');
        }
        
        $fields = array_keys($data);
        $setClause = implode(' = ?, ', $fields) . ' = ?';
        
        $sql = "UPDATE {$this->table} SET {$setClause} WHERE {$this->primaryKey} = ?";
        
        // Add tenant check if applicable
        if ($this->hasTenantColumn()) {
            $sql .= " AND {$this->tenantColumn} = ?";
            $params = array_merge(array_values($data), [$id, $this->getCurrentTenantId()]);
        } else {
            $params = array_merge(array_values($data), [$id]);
        }
        
        $stmt = $this->db->prepare($sql);
        return $stmt->execute($params);
    }
    
    /**
     * Delete record (soft delete if enabled)
     */
    public function delete($id) {
        if ($this->softDeletes) {
            return $this->softDelete($id);
        }
        
        $sql = "DELETE FROM {$this->table} WHERE {$this->primaryKey} = ?";
        
        // Add tenant check if applicable
        if ($this->hasTenantColumn()) {
            $sql .= " AND {$this->tenantColumn} = ?";
            $params = [$id, $this->getCurrentTenantId()];
        } else {
            $params = [$id];
        }
        
        $stmt = $this->db->prepare($sql);
        return $stmt->execute($params);
    }
    
    /**
     * Soft delete record
     */
    public function softDelete($id) {
        $sql = "UPDATE {$this->table} SET deleted_at = ? WHERE {$this->primaryKey} = ?";
        
        // Add tenant check if applicable
        if ($this->hasTenantColumn()) {
            $sql .= " AND {$this->tenantColumn} = ?";
            $params = [date('Y-m-d H:i:s'), $id, $this->getCurrentTenantId()];
        } else {
            $params = [date('Y-m-d H:i:s'), $id];
        }
        
        $stmt = $this->db->prepare($sql);
        return $stmt->execute($params);
    }
    
    /**
     * Restore soft deleted record
     */
    public function restore($id) {
        $sql = "UPDATE {$this->table} SET deleted_at = NULL WHERE {$this->primaryKey} = ?";
        
        // Add tenant check if applicable
        if ($this->hasTenantColumn()) {
            $sql .= " AND {$this->tenantColumn} = ?";
            $params = [$id, $this->getCurrentTenantId()];
        } else {
            $params = [$id];
        }
        
        $stmt = $this->db->prepare($sql);
        return $stmt->execute($params);
    }
    
    /**
     * Search records
     */
    public function search($query, $fields = [], $tenantId = null) {
        if (empty($fields)) {
            return [];
        }
        
        $whereConditions = [];
        $params = [];
        
        foreach ($fields as $field) {
            $whereConditions[] = "{$field} LIKE ?";
            $params[] = "%{$query}%";
        }
        
        $sql = "SELECT * FROM {$this->table} WHERE (" . implode(' OR ', $whereConditions) . ")";
        
        // Add tenant check if applicable
        if ($this->hasTenantColumn() && $tenantId !== false) {
            $tenantId = $tenantId ?: $this->getCurrentTenantId();
            $sql .= " AND {$this->tenantColumn} = ?";
            $params[] = $tenantId;
        }
        
        if ($this->softDeletes) {
            $sql .= " AND deleted_at IS NULL";
        }
        
        $sql .= " ORDER BY created_at DESC";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        
        return $stmt->fetchAll();
    }
    
    /**
     * Count records
     */
    public function count($tenantId = null) {
        $sql = "SELECT COUNT(*) as total FROM {$this->table}";
        $params = [];
        
        if ($this->hasTenantColumn() && $tenantId !== false) {
            $tenantId = $tenantId ?: $this->getCurrentTenantId();
            $sql .= " WHERE {$this->tenantColumn} = ?";
            $params[] = $tenantId;
        }
        
        if ($this->softDeletes) {
            $sql .= $this->hasTenantColumn() && $tenantId !== false ? " AND" : " WHERE";
            $sql .= " deleted_at IS NULL";
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        
        return $stmt->fetch()['total'];
    }
    
    /**
     * Filter data to only include fillable fields
     */
    protected function filterFillable($data) {
        if (empty($this->fillable)) {
            return $data;
        }
        
        return array_intersect_key($data, array_flip($this->fillable));
    }
    
    /**
     * Remove hidden fields from data
     */
    protected function hideFields($data) {
        if (empty($this->hidden)) {
            return $data;
        }
        
        return array_diff_key($data, array_flip($this->hidden));
    }
    
    /**
     * Check if model has tenant column
     */
    protected function hasTenantColumn() {
        return !empty($this->tenantColumn);
    }
    
    /**
     * Get current tenant ID
     */
    protected function getCurrentTenantId() {
        return Auth::user()['tenant_id'] ?? null;
    }
    
    /**
     * Execute raw SQL query
     */
    public function query($sql, $params = []) {
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
    }
    
    /**
     * Begin database transaction
     */
    public function beginTransaction() {
        return $this->db->beginTransaction();
    }
    
    /**
     * Commit database transaction
     */
    public function commit() {
        return $this->db->commit();
    }
    
    /**
     * Rollback database transaction
     */
    public function rollback() {
        return $this->db->rollback();
    }
}
