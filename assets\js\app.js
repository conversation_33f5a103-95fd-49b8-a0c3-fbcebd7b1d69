/**
 * Real Estate SaaS - Main JavaScript File
 * Handles global functionality and UI interactions
 */

$(document).ready(function() {
    
    // Initialize application
    initializeApp();
    
    // Global event handlers
    setupGlobalEventHandlers();
    
    // Initialize components
    initializeComponents();
    
});

/**
 * Initialize application
 */
function initializeApp() {
    // Set up CSRF token for all AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-Token': window.CSRF_TOKEN
        }
    });
    
    // Initialize tooltips
    $('[data-bs-toggle="tooltip"]').tooltip();
    
    // Initialize popovers
    $('[data-bs-toggle="popover"]').popover();
    
    // Auto-hide alerts
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
    
    // Add fade-in animation to main content
    $('.main-content').addClass('fade-in');
}

/**
 * Setup global event handlers
 */
function setupGlobalEventHandlers() {
    
    // Sidebar toggle
    $('.sidebar-toggle').on('click', function() {
        $('.sidebar').toggleClass('collapsed');
        $('.main-content').toggleClass('sidebar-collapsed');
    });
    
    // Confirmation dialogs
    $(document).on('click', '[data-confirm]', function(e) {
        e.preventDefault();
        
        const message = $(this).data('confirm') || 'هل أنت متأكد من هذا الإجراء؟';
        const href = $(this).attr('href') || $(this).data('href');
        const form = $(this).closest('form');
        
        showConfirmDialog(message, function() {
            if (href) {
                window.location.href = href;
            } else if (form.length) {
                form.submit();
            }
        });
    });
    
    // Delete confirmations
    $(document).on('click', '.btn-delete', function(e) {
        e.preventDefault();
        
        const message = 'هل أنت متأكد من حذف هذا العنصر؟ لا يمكن التراجع عن هذا الإجراء.';
        const href = $(this).attr('href') || $(this).data('href');
        
        showConfirmDialog(message, function() {
            if (href) {
                // Create a form for DELETE request
                const form = $('<form>', {
                    method: 'POST',
                    action: href
                });
                
                form.append($('<input>', {
                    type: 'hidden',
                    name: '_method',
                    value: 'DELETE'
                }));
                
                form.append($('<input>', {
                    type: 'hidden',
                    name: '_token',
                    value: window.CSRF_TOKEN
                }));
                
                $('body').append(form);
                form.submit();
            }
        });
    });
    
    // Loading states for forms
    $('form').on('submit', function() {
        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.html();
        
        submitBtn.prop('disabled', true)
                .html('<i class="fas fa-spinner fa-spin me-2"></i>جاري المعالجة...');
        
        // Store original text for restoration
        submitBtn.data('original-text', originalText);
    });
    
    // Restore button state on page load (in case of validation errors)
    $('button[type="submit"][data-original-text]').each(function() {
        $(this).prop('disabled', false).html($(this).data('original-text'));
    });
    
    // Auto-resize textareas
    $('textarea').on('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });
    
    // Number formatting for currency inputs
    $('.currency-input').on('input', function() {
        let value = $(this).val().replace(/[^\d.]/g, '');
        if (value) {
            $(this).val(formatCurrency(parseFloat(value)));
        }
    });
    
    // Phone number formatting
    $('.phone-input').on('input', function() {
        let value = $(this).val().replace(/\D/g, '');
        $(this).val(formatPhoneNumber(value));
    });
    
    // Search functionality
    $('.search-input').on('input', debounce(function() {
        const query = $(this).val();
        const target = $(this).data('target');
        
        if (query.length >= 2) {
            performSearch(query, target);
        } else {
            clearSearchResults(target);
        }
    }, 300));
    
    // Bulk actions
    $('.select-all').on('change', function() {
        const checked = $(this).is(':checked');
        $('.item-checkbox').prop('checked', checked);
        updateBulkActions();
    });
    
    $(document).on('change', '.item-checkbox', function() {
        updateBulkActions();
    });
    
    // Print functionality
    $('.btn-print').on('click', function() {
        window.print();
    });
    
    // Export functionality
    $('.btn-export').on('click', function() {
        const format = $(this).data('format') || 'excel';
        const url = $(this).data('url');
        
        if (url) {
            showLoading();
            window.location.href = url + '?format=' + format;
            hideLoading();
        }
    });
}

/**
 * Initialize components
 */
function initializeComponents() {
    
    // Initialize DataTables if present
    if ($.fn.DataTable) {
        $('.data-table').DataTable({
            language: {
                url: '/assets/js/datatables-arabic.json'
            },
            responsive: true,
            pageLength: 25,
            order: [[0, 'desc']]
        });
    }
    
    // Initialize Select2 if present
    if ($.fn.select2) {
        $('.select2').select2({
            theme: 'bootstrap-5',
            language: 'ar'
        });
    }
    
    // Initialize date pickers
    if ($.fn.datepicker) {
        $('.datepicker').datepicker({
            format: 'yyyy-mm-dd',
            language: 'ar',
            autoclose: true,
            todayHighlight: true
        });
    }
    
    // Initialize file uploads
    initializeFileUploads();
    
    // Initialize charts
    initializeCharts();
}

/**
 * Show confirmation dialog
 */
function showConfirmDialog(message, callback) {
    $('#confirmMessage').text(message);
    $('#confirmModal').modal('show');
    
    $('#confirmButton').off('click').on('click', function() {
        $('#confirmModal').modal('hide');
        if (callback) callback();
    });
}

/**
 * Show loading overlay
 */
function showLoading(message = 'جاري التحميل...') {
    $('.loading-text').text(message);
    $('#loading-overlay').removeClass('d-none');
}

/**
 * Hide loading overlay
 */
function hideLoading() {
    $('#loading-overlay').addClass('d-none');
}

/**
 * Show notification
 */
function showNotification(message, type = 'success') {
    const alertClass = type === 'error' ? 'alert-danger' : `alert-${type}`;
    const icon = getNotificationIcon(type);
    
    const alert = $(`
        <div class="alert ${alertClass} alert-dismissible fade show position-fixed" 
             style="top: 20px; left: 20px; z-index: 9999; min-width: 300px;">
            <i class="${icon} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `);
    
    $('body').append(alert);
    
    // Auto-hide after 5 seconds
    setTimeout(function() {
        alert.fadeOut(function() {
            $(this).remove();
        });
    }, 5000);
}

/**
 * Get notification icon based on type
 */
function getNotificationIcon(type) {
    const icons = {
        success: 'fas fa-check-circle',
        error: 'fas fa-exclamation-circle',
        warning: 'fas fa-exclamation-triangle',
        info: 'fas fa-info-circle'
    };
    
    return icons[type] || icons.info;
}

/**
 * Format currency
 */
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR'
    }).format(amount);
}

/**
 * Format phone number
 */
function formatPhoneNumber(phone) {
    // Remove all non-digits
    phone = phone.replace(/\D/g, '');
    
    // Format Saudi phone numbers
    if (phone.length === 9 && phone.startsWith('5')) {
        return phone.replace(/(\d{2})(\d{3})(\d{4})/, '05$1 $2 $3');
    } else if (phone.length === 12 && phone.startsWith('966')) {
        return phone.replace(/(\d{3})(\d{2})(\d{3})(\d{4})/, '+$1 $2 $3 $4');
    }
    
    return phone;
}

/**
 * Debounce function
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * Perform search
 */
function performSearch(query, target) {
    // Implementation depends on specific search requirements
    console.log('Searching for:', query, 'in:', target);
}

/**
 * Clear search results
 */
function clearSearchResults(target) {
    // Implementation depends on specific search requirements
    console.log('Clearing search results for:', target);
}

/**
 * Update bulk actions
 */
function updateBulkActions() {
    const checkedItems = $('.item-checkbox:checked').length;
    const bulkActions = $('.bulk-actions');
    
    if (checkedItems > 0) {
        bulkActions.removeClass('d-none');
        $('.selected-count').text(checkedItems);
    } else {
        bulkActions.addClass('d-none');
    }
}

/**
 * Initialize file uploads
 */
function initializeFileUploads() {
    $('.file-upload').on('change', function() {
        const files = this.files;
        const preview = $(this).siblings('.file-preview');
        
        preview.empty();
        
        for (let i = 0; i < files.length; i++) {
            const file = files[i];
            
            if (file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.append(`
                        <div class="preview-item">
                            <img src="${e.target.result}" alt="${file.name}" class="img-thumbnail">
                            <div class="preview-name">${file.name}</div>
                        </div>
                    `);
                };
                reader.readAsDataURL(file);
            } else {
                preview.append(`
                    <div class="preview-item">
                        <i class="fas fa-file fa-3x"></i>
                        <div class="preview-name">${file.name}</div>
                    </div>
                `);
            }
        }
    });
}

/**
 * Initialize charts
 */
function initializeCharts() {
    // Chart.js default configuration
    Chart.defaults.font.family = 'Cairo';
    Chart.defaults.color = '#6c757d';
    
    // Initialize specific charts based on page
    if ($('#revenueChart').length) {
        initializeRevenueChart();
    }
    
    if ($('#propertiesChart').length) {
        initializePropertiesChart();
    }
}

/**
 * Initialize revenue chart
 */
function initializeRevenueChart() {
    const ctx = document.getElementById('revenueChart').getContext('2d');
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
            datasets: [{
                label: 'الإيرادات',
                data: [12000, 19000, 15000, 25000, 22000, 30000],
                borderColor: '#667eea',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'top',
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return formatCurrency(value);
                        }
                    }
                }
            }
        }
    });
}

/**
 * Initialize properties chart
 */
function initializePropertiesChart() {
    const ctx = document.getElementById('propertiesChart').getContext('2d');
    
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['متاح', 'مباع', 'مؤجر', 'قيد التفاوض'],
            datasets: [{
                data: [45, 25, 20, 10],
                backgroundColor: [
                    '#28a745',
                    '#dc3545',
                    '#ffc107',
                    '#17a2b8'
                ]
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom',
                }
            }
        }
    });
}

/**
 * AJAX helper functions
 */
window.ajax = {
    get: function(url, data = {}) {
        return $.get(url, data);
    },
    
    post: function(url, data = {}) {
        return $.post(url, data);
    },
    
    put: function(url, data = {}) {
        return $.ajax({
            url: url,
            method: 'PUT',
            data: data
        });
    },
    
    delete: function(url) {
        return $.ajax({
            url: url,
            method: 'DELETE'
        });
    }
};

/**
 * Utility functions
 */
window.utils = {
    formatCurrency: formatCurrency,
    formatPhoneNumber: formatPhoneNumber,
    showNotification: showNotification,
    showLoading: showLoading,
    hideLoading: hideLoading,
    debounce: debounce
};
