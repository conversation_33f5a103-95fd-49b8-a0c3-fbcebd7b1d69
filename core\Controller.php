<?php
/**
 * Base Controller Class
 * All controllers extend from this base class
 */

abstract class Controller {
    protected $view;
    protected $model;
    protected $request;
    protected $user;
    
    public function __construct() {
        $this->view = new View();
        $this->request = $this->parseRequest();
        $this->user = Auth::user();
        
        // Set global view variables
        $this->view->set('user', $this->user);
        $this->view->set('currentUrl', $_SERVER['REQUEST_URI']);
        $this->view->set('csrfToken', CSRF::generateToken());
    }
    
    protected function parseRequest() {
        $request = [
            'method' => $_SERVER['REQUEST_METHOD'],
            'uri' => $_SERVER['REQUEST_URI'],
            'get' => $_GET,
            'post' => $_POST,
            'files' => $_FILES,
            'headers' => getallheaders(),
            'body' => file_get_contents('php://input')
        ];
        
        // Parse JSON body for API requests
        if (isset($request['headers']['Content-Type']) && 
            strpos($request['headers']['Content-Type'], 'application/json') !== false) {
            $request['json'] = json_decode($request['body'], true);
        }
        
        return $request;
    }
    
    protected function validate($data, $rules) {
        $validator = new Validator();
        return $validator->validate($data, $rules);
    }
    
    protected function jsonResponse($data, $statusCode = 200) {
        http_response_code($statusCode);
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    protected function successResponse($message = null, $data = null) {
        $response = ['success' => true];
        
        if ($message) {
            $response['message'] = $message;
        }
        
        if ($data) {
            $response['data'] = $data;
        }
        
        return $this->jsonResponse($response);
    }
    
    protected function errorResponse($message, $statusCode = 400, $errors = null) {
        $response = [
            'success' => false,
            'message' => $message
        ];
        
        if ($errors) {
            $response['errors'] = $errors;
        }
        
        return $this->jsonResponse($response, $statusCode);
    }
    
    protected function redirect($path, $message = null, $type = 'success') {
        if ($message) {
            Session::setFlash($type, $message);
        }
        
        header('Location: ' . $path);
        exit;
    }
    
    protected function back($message = null, $type = 'error') {
        if ($message) {
            Session::setFlash($type, $message);
        }
        
        $referer = $_SERVER['HTTP_REFERER'] ?? '/dashboard';
        header('Location: ' . $referer);
        exit;
    }
    
    protected function requirePermission($permission) {
        if (!Auth::hasPermission($permission)) {
            if ($this->isApiRequest()) {
                return $this->errorResponse('ليس لديك صلاحية للوصول', 403);
            } else {
                return $this->redirect('/dashboard', 'ليس لديك صلاحية للوصول لهذه الصفحة', 'error');
            }
        }
    }
    
    protected function requireRole($role) {
        if (!Auth::hasRole($role)) {
            if ($this->isApiRequest()) {
                return $this->errorResponse('ليس لديك صلاحية للوصول', 403);
            } else {
                return $this->redirect('/dashboard', 'ليس لديك صلاحية للوصول لهذه الصفحة', 'error');
            }
        }
    }
    
    protected function isApiRequest() {
        return strpos($_SERVER['REQUEST_URI'], '/api/') === 0 || 
               (isset($_SERVER['HTTP_ACCEPT']) && strpos($_SERVER['HTTP_ACCEPT'], 'application/json') !== false);
    }
    
    protected function uploadFile($file, $directory = 'general', $allowedTypes = null) {
        if (!isset($file) || $file['error'] !== UPLOAD_ERR_OK) {
            throw new Exception('فشل في رفع الملف');
        }
        
        // Check file size
        if ($file['size'] > MAX_FILE_SIZE) {
            throw new Exception('حجم الملف كبير جداً');
        }
        
        // Get file extension
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        
        // Check allowed types
        $allowedTypes = $allowedTypes ?: array_merge(ALLOWED_IMAGE_TYPES, ALLOWED_DOCUMENT_TYPES);
        if (!in_array($extension, $allowedTypes)) {
            throw new Exception('نوع الملف غير مدعوم');
        }
        
        // Create upload directory if it doesn't exist
        $uploadDir = UPLOADS_PATH . $directory . '/';
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }
        
        // Generate unique filename
        $filename = uniqid() . '_' . time() . '.' . $extension;
        $filepath = $uploadDir . $filename;
        
        // Move uploaded file
        if (!move_uploaded_file($file['tmp_name'], $filepath)) {
            throw new Exception('فشل في حفظ الملف');
        }
        
        return $directory . '/' . $filename;
    }
    
    protected function deleteFile($filepath) {
        $fullPath = UPLOADS_PATH . $filepath;
        if (file_exists($fullPath)) {
            return unlink($fullPath);
        }
        return false;
    }
    
    protected function paginate($query, $page = 1, $perPage = null) {
        $perPage = $perPage ?: ITEMS_PER_PAGE;
        $page = max(1, (int)$page);
        $offset = ($page - 1) * $perPage;
        
        // Get total count
        $countQuery = preg_replace('/SELECT .+ FROM/i', 'SELECT COUNT(*) as total FROM', $query);
        $totalResult = Database::getInstance()->query($countQuery);
        $total = $totalResult->fetch()['total'];
        
        // Add limit to original query
        $query .= " LIMIT {$offset}, {$perPage}";
        
        return [
            'query' => $query,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total' => $total,
                'total_pages' => ceil($total / $perPage),
                'has_prev' => $page > 1,
                'has_next' => $page < ceil($total / $perPage)
            ]
        ];
    }
    
    protected function getCurrentTenantId() {
        return Auth::user()['tenant_id'] ?? null;
    }
    
    protected function ensureTenantAccess($tenantId = null) {
        $userTenantId = $this->getCurrentTenantId();
        
        // Super admin can access all tenants
        if (Auth::hasRole(ROLE_SUPER_ADMIN)) {
            return true;
        }
        
        // Check if user belongs to the tenant
        if ($tenantId && $tenantId != $userTenantId) {
            if ($this->isApiRequest()) {
                return $this->errorResponse('ليس لديك صلاحية للوصول', 403);
            } else {
                return $this->redirect('/dashboard', 'ليس لديك صلاحية للوصول', 'error');
            }
        }
        
        return true;
    }
    
    /**
     * Override this method in child controllers to define required permissions
     */
    public function getRequiredPermissions($method) {
        return null;
    }
}
