<?php
/**
 * Activity Log Model
 * Handles activity logging and audit trail
 */

class ActivityLog extends Model {
    protected $table = 'activity_logs';
    protected $fillable = [
        'tenant_id', 'user_id', 'action', 'model', 'model_id', 'description',
        'old_values', 'new_values', 'ip_address', 'user_agent'
    ];
    protected $timestamps = true;
    protected $softDeletes = false;
    
    /**
     * Get activities for current tenant
     */
    public function getForTenant($tenantId = null, $limit = 50) {
        $tenantId = $tenantId ?: $this->getCurrentTenantId();
        
        $sql = "SELECT al.*, u.first_name, u.last_name, u.email
                FROM {$this->table} al
                LEFT JOIN users u ON al.user_id = u.id
                WHERE al.tenant_id = ?
                ORDER BY al.created_at DESC
                LIMIT ?";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$tenantId, $limit]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get activities for specific user
     */
    public function getForUser($userId, $limit = 50) {
        $sql = "SELECT al.*, u.first_name, u.last_name, u.email
                FROM {$this->table} al
                LEFT JOIN users u ON al.user_id = u.id
                WHERE al.user_id = ?
                ORDER BY al.created_at DESC
                LIMIT ?";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$userId, $limit]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get activities for specific model
     */
    public function getForModel($model, $modelId, $limit = 20) {
        $sql = "SELECT al.*, u.first_name, u.last_name, u.email
                FROM {$this->table} al
                LEFT JOIN users u ON al.user_id = u.id
                WHERE al.model = ? AND al.model_id = ?
                ORDER BY al.created_at DESC
                LIMIT ?";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$model, $modelId, $limit]);
        return $stmt->fetchAll();
    }
    
    /**
     * Log activity
     */
    public function logActivity($action, $model = null, $modelId = null, $description = null, $oldValues = null, $newValues = null) {
        $data = [
            'tenant_id' => Auth::tenantId(),
            'user_id' => Auth::id(),
            'action' => $action,
            'model' => $model,
            'model_id' => $modelId,
            'description' => $description,
            'old_values' => $oldValues ? json_encode($oldValues) : null,
            'new_values' => $newValues ? json_encode($newValues) : null,
            'ip_address' => $this->getClientIP(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null
        ];
        
        return $this->create($data);
    }
    
    /**
     * Get client IP address
     */
    private function getClientIP() {
        $ipKeys = ['HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);
                    
                    if (filter_var($ip, FILTER_VALIDATE_IP, 
                        FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    /**
     * Get activity statistics
     */
    public function getStats($tenantId = null, $days = 30) {
        $tenantId = $tenantId ?: $this->getCurrentTenantId();
        
        $stats = [];
        
        // Total activities
        $sql = "SELECT COUNT(*) as count FROM {$this->table} 
                WHERE tenant_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$tenantId, $days]);
        $stats['total'] = $stmt->fetch()['count'];
        
        // Activities by action
        $sql = "SELECT action, COUNT(*) as count 
                FROM {$this->table} 
                WHERE tenant_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
                GROUP BY action 
                ORDER BY count DESC";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$tenantId, $days]);
        $stats['by_action'] = $stmt->fetchAll();
        
        // Activities by user
        $sql = "SELECT u.first_name, u.last_name, COUNT(*) as count 
                FROM {$this->table} al
                LEFT JOIN users u ON al.user_id = u.id
                WHERE al.tenant_id = ? AND al.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
                GROUP BY al.user_id 
                ORDER BY count DESC
                LIMIT 10";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$tenantId, $days]);
        $stats['by_user'] = $stmt->fetchAll();
        
        // Daily activities
        $sql = "SELECT DATE(created_at) as date, COUNT(*) as count 
                FROM {$this->table} 
                WHERE tenant_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
                GROUP BY DATE(created_at) 
                ORDER BY date";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$tenantId, $days]);
        $stats['daily'] = $stmt->fetchAll();
        
        return $stats;
    }
    
    /**
     * Search activities
     */
    public function search($query, $filters = [], $tenantId = null) {
        $tenantId = $tenantId ?: $this->getCurrentTenantId();
        
        $whereConditions = ["al.tenant_id = ?"];
        $params = [$tenantId];
        
        // Text search
        if ($query) {
            $whereConditions[] = "(al.action LIKE ? OR al.description LIKE ? OR u.first_name LIKE ? OR u.last_name LIKE ?)";
            $params[] = "%{$query}%";
            $params[] = "%{$query}%";
            $params[] = "%{$query}%";
            $params[] = "%{$query}%";
        }
        
        // Filters
        if (isset($filters['action']) && $filters['action']) {
            $whereConditions[] = "al.action = ?";
            $params[] = $filters['action'];
        }
        
        if (isset($filters['model']) && $filters['model']) {
            $whereConditions[] = "al.model = ?";
            $params[] = $filters['model'];
        }
        
        if (isset($filters['user_id']) && $filters['user_id']) {
            $whereConditions[] = "al.user_id = ?";
            $params[] = $filters['user_id'];
        }
        
        if (isset($filters['date_from']) && $filters['date_from']) {
            $whereConditions[] = "DATE(al.created_at) >= ?";
            $params[] = $filters['date_from'];
        }
        
        if (isset($filters['date_to']) && $filters['date_to']) {
            $whereConditions[] = "DATE(al.created_at) <= ?";
            $params[] = $filters['date_to'];
        }
        
        $sql = "SELECT al.*, u.first_name, u.last_name, u.email
                FROM {$this->table} al
                LEFT JOIN users u ON al.user_id = u.id
                WHERE " . implode(' AND ', $whereConditions) . "
                ORDER BY al.created_at DESC";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
    }
    
    /**
     * Get activities with pagination
     */
    public function getPaginated($page = 1, $perPage = 50, $filters = [], $tenantId = null) {
        $tenantId = $tenantId ?: $this->getCurrentTenantId();
        $offset = ($page - 1) * $perPage;
        
        $whereConditions = ["al.tenant_id = ?"];
        $params = [$tenantId];
        
        // Apply filters
        if (isset($filters['search']) && $filters['search']) {
            $whereConditions[] = "(al.action LIKE ? OR al.description LIKE ? OR u.first_name LIKE ? OR u.last_name LIKE ?)";
            $params[] = "%{$filters['search']}%";
            $params[] = "%{$filters['search']}%";
            $params[] = "%{$filters['search']}%";
            $params[] = "%{$filters['search']}%";
        }
        
        if (isset($filters['action']) && $filters['action']) {
            $whereConditions[] = "al.action = ?";
            $params[] = $filters['action'];
        }
        
        if (isset($filters['model']) && $filters['model']) {
            $whereConditions[] = "al.model = ?";
            $params[] = $filters['model'];
        }
        
        $whereClause = implode(' AND ', $whereConditions);
        
        // Get total count
        $countSql = "SELECT COUNT(*) as total 
                     FROM {$this->table} al
                     LEFT JOIN users u ON al.user_id = u.id
                     WHERE {$whereClause}";
        $stmt = $this->db->prepare($countSql);
        $stmt->execute($params);
        $total = $stmt->fetch()['total'];
        
        // Get data
        $sql = "SELECT al.*, u.first_name, u.last_name, u.email
                FROM {$this->table} al
                LEFT JOIN users u ON al.user_id = u.id
                WHERE {$whereClause}
                ORDER BY al.created_at DESC
                LIMIT {$offset}, {$perPage}";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        $data = $stmt->fetchAll();
        
        return [
            'data' => $data,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total' => $total,
                'total_pages' => ceil($total / $perPage),
                'has_prev' => $page > 1,
                'has_next' => $page < ceil($total / $perPage)
            ]
        ];
    }
    
    /**
     * Clean old activities
     */
    public function cleanOldActivities($days = 90) {
        $sql = "DELETE FROM {$this->table} WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([$days]);
    }
    
    /**
     * Get action display name
     */
    public function getActionDisplayName($action) {
        $actions = [
            'create' => 'إنشاء',
            'update' => 'تحديث',
            'delete' => 'حذف',
            'login' => 'تسجيل دخول',
            'logout' => 'تسجيل خروج',
            'view' => 'عرض',
            'export' => 'تصدير',
            'import' => 'استيراد',
            'upload' => 'رفع ملف',
            'download' => 'تحميل',
            'send' => 'إرسال',
            'approve' => 'موافقة',
            'reject' => 'رفض',
            'restore' => 'استعادة'
        ];
        
        return $actions[$action] ?? $action;
    }
    
    /**
     * Get model display name
     */
    public function getModelDisplayName($model) {
        $models = [
            'Property' => 'عقار',
            'Client' => 'عميل',
            'Invoice' => 'فاتورة',
            'Transaction' => 'معاملة',
            'User' => 'مستخدم',
            'Tenant' => 'مكتب',
            'Setting' => 'إعداد'
        ];
        
        return $models[$model] ?? $model;
    }
}
