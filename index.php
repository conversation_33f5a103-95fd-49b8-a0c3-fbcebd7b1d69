<?php
/**
 * Real Estate SaaS System
 * Main Entry Point and Router
 *
 * This file handles all incoming requests and routes them to appropriate controllers
 *
 * <AUTHOR>
 * @version 1.0
 * @charset UTF-8
 */

// Set timezone and locale for Arabic support
date_default_timezone_set('Asia/Riyadh');
setlocale(LC_ALL, 'ar_SA.UTF-8');

// Start session with secure settings
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', 1);
ini_set('session.use_strict_mode', 1);
session_start();

// Define system constants
define('ROOT_PATH', __DIR__);
define('CONFIG_PATH', ROOT_PATH . '/config');
define('CONTROLLERS_PATH', ROOT_PATH . '/controllers');
define('MODELS_PATH', ROOT_PATH . '/models');
define('VIEWS_PATH', ROOT_PATH . '/views');
define('ASSETS_PATH', ROOT_PATH . '/assets');
define('UPLOADS_PATH', ROOT_PATH . '/uploads');
define('LOGS_PATH', ROOT_PATH . '/logs');
define('HELPERS_PATH', ROOT_PATH . '/helpers');

// Error reporting for development (disable in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include autoloader and configuration
require_once CONFIG_PATH . '/autoloader.php';
require_once CONFIG_PATH . '/config.php';
require_once CONFIG_PATH . '/database.php';
require_once HELPERS_PATH . '/Helper.php';

// Initialize router
$router = new Router();

try {
    // =============================================
    // PUBLIC ROUTES (No authentication required)
    // =============================================

    // Home page
    $router->addRoute('GET', '/', 'HomeController@index');

    // Authentication routes
    $router->addRoute('GET', '/login', 'AuthController@showLogin');
    $router->addRoute('POST', '/login', 'AuthController@login');
    $router->addRoute('GET', '/register', 'AuthController@showRegister');
    $router->addRoute('POST', '/register', 'AuthController@register');
    $router->addRoute('POST', '/logout', 'AuthController@logout');

    // Password reset routes
    $router->addRoute('GET', '/forgot-password', 'AuthController@showForgotPassword');
    $router->addRoute('POST', '/forgot-password', 'AuthController@forgotPassword');
    $router->addRoute('GET', '/reset-password/{token}', 'AuthController@showResetPassword');
    $router->addRoute('POST', '/reset-password', 'AuthController@resetPassword');

    // Email verification
    $router->addRoute('GET', '/verify-email/{token}', 'AuthController@verifyEmail');
    $router->addRoute('POST', '/resend-verification', 'AuthController@resendVerification');

    // =============================================
    // AUTHENTICATED ROUTES
    // =============================================

    // Dashboard
    $router->addRoute('GET', '/dashboard', 'DashboardController@index');

    // Properties routes
    $router->addRoute('GET', '/properties', 'PropertyController@index');
    $router->addRoute('GET', '/properties/create', 'PropertyController@create');
    $router->addRoute('POST', '/properties', 'PropertyController@store');
    $router->addRoute('GET', '/properties/{id}', 'PropertyController@show');
    $router->addRoute('GET', '/properties/{id}/edit', 'PropertyController@edit');
    $router->addRoute('PUT', '/properties/{id}', 'PropertyController@update');
    $router->addRoute('DELETE', '/properties/{id}', 'PropertyController@destroy');
    $router->addRoute('GET', '/properties/export', 'PropertyController@export');
    $router->addRoute('POST', '/properties/{id}/images', 'PropertyController@uploadImages');
    $router->addRoute('DELETE', '/properties/{id}/images/{image}', 'PropertyController@deleteImage');

    // Clients routes
    $router->addRoute('GET', '/clients', 'ClientController@index');
    $router->addRoute('GET', '/clients/create', 'ClientController@create');
    $router->addRoute('POST', '/clients', 'ClientController@store');
    $router->addRoute('GET', '/clients/{id}', 'ClientController@show');
    $router->addRoute('GET', '/clients/{id}/edit', 'ClientController@edit');
    $router->addRoute('PUT', '/clients/{id}', 'ClientController@update');
    $router->addRoute('DELETE', '/clients/{id}', 'ClientController@destroy');
    $router->addRoute('GET', '/clients/export', 'ClientController@export');
    $router->addRoute('GET', '/clients/{id}/properties', 'ClientController@matchingProperties');

    // Client interactions
    $router->addRoute('POST', '/clients/{id}/interactions', 'ClientInteractionController@store');
    $router->addRoute('PUT', '/interactions/{id}', 'ClientInteractionController@update');
    $router->addRoute('DELETE', '/interactions/{id}', 'ClientInteractionController@destroy');

    // Invoices routes
    $router->addRoute('GET', '/invoices', 'InvoiceController@index');
    $router->addRoute('GET', '/invoices/create', 'InvoiceController@create');
    $router->addRoute('POST', '/invoices', 'InvoiceController@store');
    $router->addRoute('GET', '/invoices/{id}', 'InvoiceController@show');
    $router->addRoute('GET', '/invoices/{id}/edit', 'InvoiceController@edit');
    $router->addRoute('PUT', '/invoices/{id}', 'InvoiceController@update');
    $router->addRoute('DELETE', '/invoices/{id}', 'InvoiceController@destroy');
    $router->addRoute('GET', '/invoices/{id}/print', 'InvoiceController@print');
    $router->addRoute('GET', '/invoices/{id}/pdf', 'InvoiceController@pdf');
    $router->addRoute('POST', '/invoices/{id}/payment', 'InvoiceController@recordPayment');

    // Transactions routes
    $router->addRoute('GET', '/transactions', 'TransactionController@index');
    $router->addRoute('GET', '/transactions/create', 'TransactionController@create');
    $router->addRoute('POST', '/transactions', 'TransactionController@store');
    $router->addRoute('GET', '/transactions/{id}', 'TransactionController@show');
    $router->addRoute('GET', '/transactions/{id}/edit', 'TransactionController@edit');
    $router->addRoute('PUT', '/transactions/{id}', 'TransactionController@update');
    $router->addRoute('DELETE', '/transactions/{id}', 'TransactionController@destroy');

    // Reports routes
    $router->addRoute('GET', '/reports', 'ReportController@index');
    $router->addRoute('GET', '/reports/properties', 'ReportController@properties');
    $router->addRoute('GET', '/reports/clients', 'ReportController@clients');
    $router->addRoute('GET', '/reports/financial', 'ReportController@financial');
    $router->addRoute('GET', '/reports/export', 'ReportController@export');

    // Users routes
    $router->addRoute('GET', '/users', 'UserController@index');
    $router->addRoute('GET', '/users/create', 'UserController@create');
    $router->addRoute('POST', '/users', 'UserController@store');
    $router->addRoute('GET', '/users/{id}', 'UserController@show');
    $router->addRoute('GET', '/users/{id}/edit', 'UserController@edit');
    $router->addRoute('PUT', '/users/{id}', 'UserController@update');
    $router->addRoute('DELETE', '/users/{id}', 'UserController@destroy');
    $router->addRoute('POST', '/users/{id}/permissions', 'UserController@updatePermissions');

    // Profile routes
    $router->addRoute('GET', '/profile', 'ProfileController@show');
    $router->addRoute('GET', '/profile/edit', 'ProfileController@edit');
    $router->addRoute('PUT', '/profile', 'ProfileController@update');
    $router->addRoute('POST', '/profile/avatar', 'ProfileController@uploadAvatar');
    $router->addRoute('PUT', '/profile/password', 'ProfileController@changePassword');

    // Settings routes
    $router->addRoute('GET', '/settings', 'SettingsController@index');
    $router->addRoute('PUT', '/settings', 'SettingsController@update');
    $router->addRoute('GET', '/settings/backup', 'SettingsController@backup');
    $router->addRoute('POST', '/settings/backup', 'SettingsController@createBackup');


    // =============================================
    // SUPER ADMIN ROUTES
    // =============================================

    // Admin dashboard
    $router->addRoute('GET', '/admin', 'AdminController@dashboard');
    $router->addRoute('GET', '/admin/dashboard', 'AdminController@dashboard');

    // Tenants (Offices) management
    $router->addRoute('GET', '/admin/tenants', 'AdminController@tenants');
    $router->addRoute('GET', '/admin/tenants/create', 'AdminController@createTenant');
    $router->addRoute('POST', '/admin/tenants', 'AdminController@storeTenant');
    $router->addRoute('GET', '/admin/tenants/{id}', 'AdminController@showTenant');
    $router->addRoute('GET', '/admin/tenants/{id}/edit', 'AdminController@editTenant');
    $router->addRoute('PUT', '/admin/tenants/{id}', 'AdminController@updateTenant');
    $router->addRoute('DELETE', '/admin/tenants/{id}', 'AdminController@destroyTenant');
    $router->addRoute('POST', '/admin/tenants/{id}/suspend', 'AdminController@suspendTenant');
    $router->addRoute('POST', '/admin/tenants/{id}/activate', 'AdminController@activateTenant');

    // System management
    $router->addRoute('GET', '/admin/logs', 'AdminController@logs');
    $router->addRoute('GET', '/admin/backup', 'AdminController@backup');
    $router->addRoute('POST', '/admin/backup', 'AdminController@createBackup');
    $router->addRoute('GET', '/admin/settings', 'AdminController@settings');
    $router->addRoute('PUT', '/admin/settings', 'AdminController@updateSettings');

    // =============================================
    // API ROUTES
    // =============================================

    // API Authentication
    $router->addRoute('POST', '/api/auth/login', 'Api\\AuthController@login');
    $router->addRoute('POST', '/api/auth/logout', 'Api\\AuthController@logout');
    $router->addRoute('POST', '/api/auth/refresh', 'Api\\AuthController@refresh');

    // API Properties
    $router->addRoute('GET', '/api/properties', 'Api\\PropertyController@index');
    $router->addRoute('POST', '/api/properties', 'Api\\PropertyController@store');
    $router->addRoute('GET', '/api/properties/{id}', 'Api\\PropertyController@show');
    $router->addRoute('PUT', '/api/properties/{id}', 'Api\\PropertyController@update');
    $router->addRoute('DELETE', '/api/properties/{id}', 'Api\\PropertyController@destroy');

    // API Clients
    $router->addRoute('GET', '/api/clients', 'Api\\ClientController@index');
    $router->addRoute('POST', '/api/clients', 'Api\\ClientController@store');
    $router->addRoute('GET', '/api/clients/{id}', 'Api\\ClientController@show');
    $router->addRoute('PUT', '/api/clients/{id}', 'Api\\ClientController@update');
    $router->addRoute('DELETE', '/api/clients/{id}', 'Api\\ClientController@destroy');

    // API Search
    $router->addRoute('GET', '/api/search', 'Api\\SearchController@search');
    $router->addRoute('GET', '/api/search/properties', 'Api\\SearchController@properties');
    $router->addRoute('GET', '/api/search/clients', 'Api\\SearchController@clients');

    // =============================================
    // UTILITY ROUTES
    // =============================================

    // File uploads
    $router->addRoute('POST', '/upload', 'UploadController@upload');
    $router->addRoute('DELETE', '/upload/{file}', 'UploadController@delete');

    // Notifications
    $router->addRoute('GET', '/notifications', 'NotificationController@index');
    $router->addRoute('POST', '/notifications/{id}/read', 'NotificationController@markAsRead');
    $router->addRoute('POST', '/notifications/read-all', 'NotificationController@markAllAsRead');

    // Help and support
    $router->addRoute('GET', '/help', 'HelpController@index');
    $router->addRoute('GET', '/support', 'SupportController@index');
    $router->addRoute('POST', '/support', 'SupportController@submit');

    // Legal pages
    $router->addRoute('GET', '/privacy', 'LegalController@privacy');
    $router->addRoute('GET', '/terms', 'LegalController@terms');

    // Handle the request
    $router->handleRequest();

} catch (Exception $e) {
    // Log the error
    error_log('Application Error: ' . $e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine());

    // Show error page
    http_response_code(500);

    if (defined('DEBUG') && DEBUG) {
        // Show detailed error in debug mode
        echo '<h1>Application Error</h1>';
        echo '<p><strong>Message:</strong> ' . htmlspecialchars($e->getMessage()) . '</p>';
        echo '<p><strong>File:</strong> ' . htmlspecialchars($e->getFile()) . '</p>';
        echo '<p><strong>Line:</strong> ' . $e->getLine() . '</p>';
        echo '<h3>Stack Trace:</h3>';
        echo '<pre>' . htmlspecialchars($e->getTraceAsString()) . '</pre>';
    } else {
        // Show generic error page
        if (file_exists(VIEWS_PATH . '/errors/500.php')) {
            include VIEWS_PATH . '/errors/500.php';
        } else {
            echo '<h1>خطأ في النظام</h1><p>حدث خطأ غير متوقع. يرجى المحاولة لاحقاً.</p>';
        }
    }
}
