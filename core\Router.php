<?php
/**
 * Router Class
 * Handles URL routing and request dispatching
 */

class Router {
    private $routes = [];
    private $currentRoute = null;
    
    public function addRoute($method, $path, $handler) {
        $this->routes[] = [
            'method' => strtoupper($method),
            'path' => $path,
            'handler' => $handler,
            'pattern' => $this->convertToPattern($path)
        ];
    }
    
    private function convertToPattern($path) {
        // Convert {id} to regex pattern
        $pattern = preg_replace('/\{([^}]+)\}/', '([^/]+)', $path);
        $pattern = str_replace('/', '\/', $pattern);
        return '/^' . $pattern . '$/';
    }
    
    public function handleRequest() {
        $method = $_SERVER['REQUEST_METHOD'];
        $path = $this->getCurrentPath();
        
        // Handle method override for PUT/DELETE
        if ($method === 'POST' && isset($_POST['_method'])) {
            $method = strtoupper($_POST['_method']);
        }
        
        foreach ($this->routes as $route) {
            if ($route['method'] === $method && preg_match($route['pattern'], $path, $matches)) {
                array_shift($matches); // Remove full match
                $this->currentRoute = $route;
                return $this->dispatch($route['handler'], $matches);
            }
        }
        
        // Route not found
        $this->handleNotFound();
    }
    
    private function getCurrentPath() {
        $path = $_SERVER['REQUEST_URI'];
        
        // Remove query string
        if (($pos = strpos($path, '?')) !== false) {
            $path = substr($path, 0, $pos);
        }
        
        // Remove base path if running in subdirectory
        $basePath = dirname($_SERVER['SCRIPT_NAME']);
        if ($basePath !== '/' && strpos($path, $basePath) === 0) {
            $path = substr($path, strlen($basePath));
        }
        
        return $path ?: '/';
    }
    
    private function dispatch($handler, $params = []) {
        if (is_string($handler)) {
            list($controllerName, $methodName) = explode('@', $handler);
            
            // Handle API controllers
            if (strpos($controllerName, 'Api\\') === 0) {
                $controllerFile = ROOT_PATH . '/api/' . str_replace('Api\\', '', $controllerName) . '.php';
            } else {
                $controllerFile = CONTROLLERS_PATH . '/' . $controllerName . '.php';
            }
            
            if (!file_exists($controllerFile)) {
                throw new Exception("Controller file not found: {$controllerFile}");
            }
            
            require_once $controllerFile;
            
            if (!class_exists($controllerName)) {
                throw new Exception("Controller class not found: {$controllerName}");
            }
            
            $controller = new $controllerName();
            
            if (!method_exists($controller, $methodName)) {
                throw new Exception("Method not found: {$controllerName}@{$methodName}");
            }
            
            // Check authentication and permissions
            $this->checkAuthentication($controller, $methodName);
            
            return call_user_func_array([$controller, $methodName], $params);
        }
        
        if (is_callable($handler)) {
            return call_user_func_array($handler, $params);
        }
        
        throw new Exception("Invalid route handler");
    }
    
    private function checkAuthentication($controller, $method) {
        // Skip authentication for public routes
        $publicRoutes = [
            'AuthController@showLogin',
            'AuthController@login',
            'AuthController@showRegister',
            'AuthController@register',
            'HomeController@index'
        ];
        
        $currentHandler = get_class($controller) . '@' . $method;
        
        if (in_array($currentHandler, $publicRoutes)) {
            return;
        }
        
        // Check if user is authenticated
        if (!Auth::check()) {
            if ($this->isApiRequest()) {
                http_response_code(401);
                echo json_encode(['error' => 'غير مصرح', 'message' => 'يجب تسجيل الدخول أولاً']);
                exit;
            } else {
                Session::setFlash('error', 'يجب تسجيل الدخول أولاً');
                header('Location: /login');
                exit;
            }
        }
        
        // Check permissions if controller has permission requirements
        if (method_exists($controller, 'getRequiredPermissions')) {
            $requiredPermissions = $controller->getRequiredPermissions($method);
            if ($requiredPermissions && !Auth::hasPermission($requiredPermissions)) {
                if ($this->isApiRequest()) {
                    http_response_code(403);
                    echo json_encode(['error' => 'ممنوع', 'message' => 'ليس لديك صلاحية للوصول']);
                    exit;
                } else {
                    Session::setFlash('error', 'ليس لديك صلاحية للوصول لهذه الصفحة');
                    header('Location: /dashboard');
                    exit;
                }
            }
        }
    }
    
    private function isApiRequest() {
        return strpos($_SERVER['REQUEST_URI'], '/api/') === 0 || 
               (isset($_SERVER['HTTP_ACCEPT']) && strpos($_SERVER['HTTP_ACCEPT'], 'application/json') !== false);
    }
    
    private function handleNotFound() {
        http_response_code(404);
        
        if ($this->isApiRequest()) {
            echo json_encode(['error' => 'غير موجود', 'message' => 'الصفحة المطلوبة غير موجودة']);
        } else {
            include VIEWS_PATH . '/errors/404.php';
        }
        exit;
    }
    
    public function getCurrentRoute() {
        return $this->currentRoute;
    }
    
    public function url($path, $params = []) {
        $url = rtrim(APP_URL, '/') . '/' . ltrim($path, '/');
        
        if (!empty($params)) {
            $url .= '?' . http_build_query($params);
        }
        
        return $url;
    }
    
    public function redirect($path, $statusCode = 302) {
        $url = $this->url($path);
        header("Location: {$url}", true, $statusCode);
        exit;
    }
    
    public function back() {
        $referer = $_SERVER['HTTP_REFERER'] ?? '/dashboard';
        header("Location: {$referer}");
        exit;
    }
}
