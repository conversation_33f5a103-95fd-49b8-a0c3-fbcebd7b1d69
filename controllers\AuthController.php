<?php
/**
 * Authentication Controller
 * Handles user login, logout, and registration
 */

class AuthController extends Controller {
    
    public function __construct() {
        parent::__construct();
        $this->model = new User();
    }
    
    /**
     * Show login form
     */
    public function showLogin() {
        // Redirect if already authenticated
        if (Auth::check()) {
            return $this->redirect('/dashboard');
        }
        
        $this->view->set('title', 'تسجيل الدخول - ' . APP_NAME);
        $this->view->setLayout('layouts/auth');
        $this->view->render('auth/login');
    }
    
    /**
     * Handle login request
     */
    public function login() {
        try {
            // Validate CSRF token
            CSRF::validateForm();
            
            // Validate input
            $validation = $this->validate($_POST, [
                'email' => 'required|email',
                'password' => 'required|min_length:6'
            ]);
            
            if (!$validation) {
                Session::setErrors($this->validator->getErrors());
                Session::flashInput($_POST);
                return $this->back();
            }
            
            $email = $_POST['email'];
            $password = $_POST['password'];
            $remember = isset($_POST['remember']);
            
            // Attempt login
            if (Auth::attempt($email, $password, $remember)) {
                // Successful login
                $intendedUrl = Session::getIntendedUrl('/dashboard');
                return $this->redirect($intendedUrl, 'مرحباً بك، تم تسجيل الدخول بنجاح');
            } else {
                return $this->back('بيانات الدخول غير صحيحة');
            }
            
        } catch (Exception $e) {
            error_log('Login error: ' . $e->getMessage());
            return $this->back($e->getMessage());
        }
    }
    
    /**
     * Handle logout request
     */
    public function logout() {
        Auth::logout();
        return $this->redirect('/login', 'تم تسجيل الخروج بنجاح');
    }
    
    /**
     * Show registration form
     */
    public function showRegister() {
        // Redirect if already authenticated
        if (Auth::check()) {
            return $this->redirect('/dashboard');
        }
        
        $this->view->set('title', 'إنشاء حساب جديد - ' . APP_NAME);
        $this->view->setLayout('layouts/auth');
        $this->view->render('auth/register');
    }
    
    /**
     * Handle registration request
     */
    public function register() {
        try {
            // Validate CSRF token
            CSRF::validateForm();
            
            // Validate input
            $validation = $this->validate($_POST, [
                'office_name' => 'required|min_length:3|max_length:255',
                'first_name' => 'required|min_length:2|max_length:100',
                'last_name' => 'required|min_length:2|max_length:100',
                'email' => 'required|email|unique:users,email',
                'phone' => 'required|phone',
                'password' => 'required|min_length:8|confirmed',
                'terms' => 'required'
            ]);
            
            if (!$validation) {
                Session::setErrors($this->validator->getErrors());
                Session::flashInput($_POST);
                return $this->back();
            }
            
            $this->model->beginTransaction();
            
            try {
                // Create tenant (office)
                $tenantModel = new Tenant();
                $tenantData = [
                    'name' => $_POST['office_name'],
                    'slug' => $tenantModel->generateSlug($_POST['office_name']),
                    'email' => $_POST['email'],
                    'phone' => $_POST['phone'],
                    'status' => 'active',
                    'subscription_expires_at' => date('Y-m-d H:i:s', strtotime('+1 month')) // 1 month trial
                ];
                
                $tenantId = $tenantModel->create($tenantData);
                
                // Create user
                $userData = [
                    'tenant_id' => $tenantId,
                    'first_name' => $_POST['first_name'],
                    'last_name' => $_POST['last_name'],
                    'email' => $_POST['email'],
                    'phone' => $_POST['phone'],
                    'password' => Auth::hashPassword($_POST['password']),
                    'role' => ROLE_OFFICE_OWNER,
                    'status' => 'active',
                    'email_verified_at' => date('Y-m-d H:i:s')
                ];
                
                $userId = $this->model->create($userData);
                
                // Assign default permissions
                $permissionModel = new Permission();
                $permissionModel->assignDefaultPermissions($userId, ROLE_OFFICE_OWNER);
                
                $this->model->commit();
                
                // Log the user in
                $user = $this->model->find($userId);
                Auth::login($user);
                
                return $this->redirect('/dashboard', 'تم إنشاء الحساب بنجاح! مرحباً بك في نظام إدارة المكاتب العقارية');
                
            } catch (Exception $e) {
                $this->model->rollback();
                throw $e;
            }
            
        } catch (Exception $e) {
            error_log('Registration error: ' . $e->getMessage());
            return $this->back('حدث خطأ أثناء إنشاء الحساب. يرجى المحاولة مرة أخرى');
        }
    }
    
    /**
     * Show forgot password form
     */
    public function showForgotPassword() {
        if (Auth::check()) {
            return $this->redirect('/dashboard');
        }
        
        $this->view->set('title', 'نسيت كلمة المرور - ' . APP_NAME);
        $this->view->setLayout('layouts/auth');
        $this->view->render('auth/forgot-password');
    }
    
    /**
     * Handle forgot password request
     */
    public function forgotPassword() {
        try {
            // Validate CSRF token
            CSRF::validateForm();
            
            // Validate input
            $validation = $this->validate($_POST, [
                'email' => 'required|email'
            ]);
            
            if (!$validation) {
                Session::setErrors($this->validator->getErrors());
                return $this->back();
            }
            
            $email = $_POST['email'];
            $user = $this->model->findByEmail($email);
            
            if (!$user) {
                return $this->back('البريد الإلكتروني غير مسجل في النظام');
            }
            
            // Generate reset token
            $token = bin2hex(random_bytes(32));
            $expiry = date('Y-m-d H:i:s', strtotime('+1 hour'));
            
            // Store reset token (you would need to create a password_resets table)
            // For now, we'll just show a success message
            
            return $this->back('تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني', 'success');
            
        } catch (Exception $e) {
            error_log('Forgot password error: ' . $e->getMessage());
            return $this->back('حدث خطأ. يرجى المحاولة مرة أخرى');
        }
    }
    
    /**
     * Show reset password form
     */
    public function showResetPassword($token) {
        if (Auth::check()) {
            return $this->redirect('/dashboard');
        }
        
        // Validate token (you would check against password_resets table)
        
        $this->view->set('title', 'إعادة تعيين كلمة المرور - ' . APP_NAME);
        $this->view->set('token', $token);
        $this->view->setLayout('layouts/auth');
        $this->view->render('auth/reset-password');
    }
    
    /**
     * Handle reset password request
     */
    public function resetPassword() {
        try {
            // Validate CSRF token
            CSRF::validateForm();
            
            // Validate input
            $validation = $this->validate($_POST, [
                'token' => 'required',
                'email' => 'required|email',
                'password' => 'required|min_length:8|confirmed'
            ]);
            
            if (!$validation) {
                Session::setErrors($this->validator->getErrors());
                return $this->back();
            }
            
            // Validate token and update password
            // Implementation would check password_resets table
            
            return $this->redirect('/login', 'تم تغيير كلمة المرور بنجاح. يمكنك الآن تسجيل الدخول');
            
        } catch (Exception $e) {
            error_log('Reset password error: ' . $e->getMessage());
            return $this->back('حدث خطأ. يرجى المحاولة مرة أخرى');
        }
    }
    
    /**
     * Verify email address
     */
    public function verifyEmail($token) {
        // Implementation for email verification
        // Would check email_verifications table and update user
        
        return $this->redirect('/dashboard', 'تم تأكيد البريد الإلكتروني بنجاح');
    }
    
    /**
     * Resend email verification
     */
    public function resendVerification() {
        if (!Auth::check()) {
            return $this->redirect('/login');
        }
        
        $user = Auth::user();
        
        if ($user['email_verified_at']) {
            return $this->back('البريد الإلكتروني مؤكد بالفعل');
        }
        
        // Send verification email
        // Implementation would generate token and send email
        
        return $this->back('تم إرسال رابط التأكيد إلى بريدك الإلكتروني', 'success');
    }
}
