<?php
/**
 * Tenant Model
 * Handles tenant (office) data and operations
 */

class Tenant extends Model {
    protected $table = 'tenants';
    protected $fillable = [
        'name', 'slug', 'email', 'phone', 'address', 'city', 'country',
        'logo', 'website', 'tax_number', 'commercial_register', 'settings', 'status'
    ];
    protected $timestamps = true;
    protected $softDeletes = true;
    protected $tenantColumn = null; // Tenants table doesn't have tenant_id
    
    /**
     * Find tenant by slug
     */
    public function findBySlug($slug) {
        $sql = "SELECT * FROM {$this->table} WHERE slug = ? AND deleted_at IS NULL";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$slug]);
        return $stmt->fetch();
    }
    
    /**
     * Check if slug exists
     */
    public function slugExists($slug, $excludeId = null) {
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE slug = ? AND deleted_at IS NULL";
        $params = [$slug];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        $result = $stmt->fetch();
        
        return $result['count'] > 0;
    }
    
    /**
     * Check if email exists
     */
    public function emailExists($email, $excludeId = null) {
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE email = ? AND deleted_at IS NULL";
        $params = [$email];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        $result = $stmt->fetch();
        
        return $result['count'] > 0;
    }
    
    /**
     * Generate unique slug
     */
    public function generateSlug($name, $excludeId = null) {
        $baseSlug = slug($name);
        $slug = $baseSlug;
        $counter = 1;
        
        while ($this->slugExists($slug, $excludeId)) {
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }
        
        return $slug;
    }
    
    /**
     * Get active tenants
     */
    public function getActive() {
        $sql = "SELECT * FROM {$this->table} WHERE status = 'active' AND deleted_at IS NULL ORDER BY name";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    /**
     * Get tenant with statistics
     */
    public function getWithStats($tenantId) {
        $tenant = $this->find($tenantId);
        
        if (!$tenant) {
            return null;
        }
        
        // Get statistics
        $stats = $this->getStats($tenantId);
        $tenant['stats'] = $stats;
        
        return $tenant;
    }
    
    /**
     * Get tenant statistics
     */
    public function getStats($tenantId) {
        $stats = [];
        
        // Users count
        $sql = "SELECT COUNT(*) as count FROM users WHERE tenant_id = ? AND deleted_at IS NULL";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$tenantId]);
        $stats['users'] = $stmt->fetch()['count'];
        
        // Properties count
        $sql = "SELECT COUNT(*) as count FROM properties WHERE tenant_id = ? AND deleted_at IS NULL";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$tenantId]);
        $stats['properties'] = $stmt->fetch()['count'];
        
        // Clients count
        $sql = "SELECT COUNT(*) as count FROM clients WHERE tenant_id = ? AND deleted_at IS NULL";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$tenantId]);
        $stats['clients'] = $stmt->fetch()['count'];
        
        // Invoices count
        $sql = "SELECT COUNT(*) as count FROM invoices WHERE tenant_id = ? AND deleted_at IS NULL";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$tenantId]);
        $stats['invoices'] = $stmt->fetch()['count'];
        
        // Total revenue this month
        $sql = "SELECT COALESCE(SUM(total_amount), 0) as total 
                FROM invoices 
                WHERE tenant_id = ? 
                AND status = 'مدفوعة' 
                AND MONTH(paid_date) = MONTH(CURRENT_DATE()) 
                AND YEAR(paid_date) = YEAR(CURRENT_DATE())
                AND deleted_at IS NULL";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$tenantId]);
        $stats['revenue_this_month'] = $stmt->fetch()['total'];
        
        // Properties by status
        $sql = "SELECT status, COUNT(*) as count 
                FROM properties 
                WHERE tenant_id = ? AND deleted_at IS NULL 
                GROUP BY status";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$tenantId]);
        $stats['properties_by_status'] = $stmt->fetchAll();
        
        return $stats;
    }
    
    /**
     * Get tenant settings
     */
    public function getSettings($tenantId) {
        $tenant = $this->find($tenantId);
        
        if (!$tenant || !$tenant['settings']) {
            return [];
        }
        
        return json_decode($tenant['settings'], true);
    }
    
    /**
     * Update tenant settings
     */
    public function updateSettings($tenantId, $settings) {
        $sql = "UPDATE {$this->table} SET settings = ?, updated_at = NOW() WHERE id = ?";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([json_encode($settings), $tenantId]);
    }
    
    /**
     * Get tenant logo URL
     */
    public function getLogoUrl($tenant) {
        if ($tenant['logo']) {
            return url('uploads/' . $tenant['logo']);
        }
        
        return null;
    }
    
    /**
     * Search tenants
     */
    public function search($query) {
        $sql = "SELECT * FROM {$this->table} 
                WHERE (name LIKE ? OR email LIKE ? OR phone LIKE ?) 
                AND deleted_at IS NULL 
                ORDER BY name";
        
        $searchTerm = "%{$query}%";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$searchTerm, $searchTerm, $searchTerm]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get tenants with pagination
     */
    public function getPaginated($page = 1, $perPage = 20, $status = null) {
        $offset = ($page - 1) * $perPage;
        
        $whereClause = "WHERE deleted_at IS NULL";
        $params = [];
        
        if ($status) {
            $whereClause .= " AND status = ?";
            $params[] = $status;
        }
        
        // Get total count
        $countSql = "SELECT COUNT(*) as total FROM {$this->table} {$whereClause}";
        $stmt = $this->db->prepare($countSql);
        $stmt->execute($params);
        $total = $stmt->fetch()['total'];
        
        // Get data
        $sql = "SELECT * FROM {$this->table} {$whereClause} ORDER BY created_at DESC LIMIT {$offset}, {$perPage}";
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        $data = $stmt->fetchAll();
        
        return [
            'data' => $data,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total' => $total,
                'total_pages' => ceil($total / $perPage),
                'has_prev' => $page > 1,
                'has_next' => $page < ceil($total / $perPage)
            ]
        ];
    }
    
    /**
     * Check subscription status
     */
    public function isSubscriptionActive($tenantId) {
        $tenant = $this->find($tenantId);
        
        if (!$tenant) {
            return false;
        }
        
        if ($tenant['status'] !== 'active') {
            return false;
        }
        
        if ($tenant['subscription_expires_at'] && strtotime($tenant['subscription_expires_at']) < time()) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Extend subscription
     */
    public function extendSubscription($tenantId, $months = 1) {
        $tenant = $this->find($tenantId);
        
        if (!$tenant) {
            return false;
        }
        
        $currentExpiry = $tenant['subscription_expires_at'] ? strtotime($tenant['subscription_expires_at']) : time();
        $newExpiry = strtotime("+{$months} months", $currentExpiry);
        
        $sql = "UPDATE {$this->table} SET subscription_expires_at = ?, updated_at = NOW() WHERE id = ?";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([date('Y-m-d H:i:s', $newExpiry), $tenantId]);
    }
    
    /**
     * Get expiring subscriptions
     */
    public function getExpiringSubscriptions($days = 30) {
        $sql = "SELECT * FROM {$this->table} 
                WHERE status = 'active' 
                AND subscription_expires_at IS NOT NULL 
                AND subscription_expires_at <= DATE_ADD(NOW(), INTERVAL ? DAY)
                AND subscription_expires_at > NOW()
                AND deleted_at IS NULL 
                ORDER BY subscription_expires_at";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$days]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get system statistics
     */
    public function getSystemStats() {
        $stats = [];
        
        // Total tenants
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE deleted_at IS NULL";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        $stats['total_tenants'] = $stmt->fetch()['count'];
        
        // Active tenants
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE status = 'active' AND deleted_at IS NULL";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        $stats['active_tenants'] = $stmt->fetch()['count'];
        
        // Tenants by status
        $sql = "SELECT status, COUNT(*) as count FROM {$this->table} WHERE deleted_at IS NULL GROUP BY status";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        $stats['tenants_by_status'] = $stmt->fetchAll();
        
        // Recent tenants
        $sql = "SELECT * FROM {$this->table} WHERE deleted_at IS NULL ORDER BY created_at DESC LIMIT 5";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        $stats['recent_tenants'] = $stmt->fetchAll();
        
        return $stats;
    }
}
