<aside class="sidebar">
    
    <!-- Sidebar Header -->
    <div class="sidebar-header">
        <div class="d-flex align-items-center">
            <div class="flex-shrink-0">
                <?php if ($user['tenant_id']): ?>
                    <?php
                    $tenantModel = new Tenant();
                    $tenant = $tenantModel->find($user['tenant_id']);
                    ?>
                    <?php if ($tenant && $tenant['logo']): ?>
                        <img src="<?= $tenantModel->getLogoUrl($tenant) ?>" 
                             alt="<?= e($tenant['name']) ?>" 
                             class="rounded" 
                             width="40" 
                             height="40">
                    <?php else: ?>
                        <div class="bg-primary rounded d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                            <i class="fas fa-building text-white"></i>
                        </div>
                    <?php endif; ?>
                <?php else: ?>
                    <div class="bg-primary rounded d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                        <i class="fas fa-crown text-white"></i>
                    </div>
                <?php endif; ?>
            </div>
            <div class="flex-grow-1 ms-3">
                <h6 class="mb-0">
                    <?php if ($user['tenant_id'] && isset($tenant)): ?>
                        <?= e($tenant['name']) ?>
                    <?php else: ?>
                        إدارة النظام
                    <?php endif; ?>
                </h6>
                <small class="text-muted">
                    <?php
                    $roleNames = [
                        'super_admin' => 'مدير النظام',
                        'office_owner' => 'مالك المكتب',
                        'employee' => 'موظف'
                    ];
                    echo $roleNames[$user['role']] ?? $user['role'];
                    ?>
                </small>
            </div>
        </div>
    </div>
    
    <!-- Sidebar Menu -->
    <nav class="sidebar-menu">
        
        <!-- Dashboard -->
        <div class="menu-item">
            <a href="/dashboard" class="menu-link <?= $this->active('/dashboard') ?>">
                <i class="fas fa-tachometer-alt menu-icon"></i>
                <span class="menu-text">لوحة التحكم</span>
            </a>
        </div>
        
        <!-- Properties Section -->
        <?php if (can('properties.view')): ?>
            <div class="menu-item">
                <a href="/properties" class="menu-link <?= $this->active('/properties') ?>">
                    <i class="fas fa-home menu-icon"></i>
                    <span class="menu-text">العقارات</span>
                    <?php
                    // Get properties count for current tenant
                    if ($user['tenant_id']) {
                        $propertyModel = new Property();
                        $propertiesCount = $propertyModel->count($user['tenant_id']);
                        if ($propertiesCount > 0) {
                            echo '<span class="menu-badge badge bg-primary">' . $propertiesCount . '</span>';
                        }
                    }
                    ?>
                </a>
            </div>
        <?php endif; ?>
        
        <!-- Clients Section -->
        <?php if (can('clients.view')): ?>
            <div class="menu-item">
                <a href="/clients" class="menu-link <?= $this->active('/clients') ?>">
                    <i class="fas fa-users menu-icon"></i>
                    <span class="menu-text">العملاء</span>
                    <?php
                    // Get clients count for current tenant
                    if ($user['tenant_id']) {
                        $clientModel = new Client();
                        $clientsCount = $clientModel->count($user['tenant_id']);
                        if ($clientsCount > 0) {
                            echo '<span class="menu-badge badge bg-success">' . $clientsCount . '</span>';
                        }
                    }
                    ?>
                </a>
            </div>
        <?php endif; ?>
        
        <!-- Invoices Section -->
        <?php if (can('invoices.view')): ?>
            <div class="menu-item">
                <a href="/invoices" class="menu-link <?= $this->active('/invoices') ?>">
                    <i class="fas fa-file-invoice menu-icon"></i>
                    <span class="menu-text">الفواتير</span>
                    <?php
                    // Get pending invoices count
                    if ($user['tenant_id']) {
                        $invoiceModel = new Invoice();
                        $pendingCount = $invoiceModel->query(
                            "SELECT COUNT(*) as count FROM invoices 
                             WHERE tenant_id = ? AND status IN ('مرسلة', 'متأخرة') AND deleted_at IS NULL",
                            [$user['tenant_id']]
                        )[0]['count'];
                        
                        if ($pendingCount > 0) {
                            echo '<span class="menu-badge badge bg-warning">' . $pendingCount . '</span>';
                        }
                    }
                    ?>
                </a>
            </div>
        <?php endif; ?>
        
        <!-- Transactions Section -->
        <?php if (can('transactions.view')): ?>
            <div class="menu-item">
                <a href="/transactions" class="menu-link <?= $this->active('/transactions') ?>">
                    <i class="fas fa-exchange-alt menu-icon"></i>
                    <span class="menu-text">المعاملات</span>
                </a>
            </div>
        <?php endif; ?>
        
        <!-- Reports Section -->
        <?php if (can('reports.view')): ?>
            <div class="menu-item">
                <a href="/reports" class="menu-link <?= $this->active('/reports') ?>">
                    <i class="fas fa-chart-bar menu-icon"></i>
                    <span class="menu-text">التقارير</span>
                </a>
            </div>
        <?php endif; ?>
        
        <!-- Divider -->
        <hr class="my-3">
        
        <!-- Management Section -->
        <?php if (can('users.view') || hasRole('super_admin')): ?>
            <div class="menu-section">
                <small class="text-muted px-3 mb-2 d-block">الإدارة</small>
                
                <!-- Users -->
                <?php if (can('users.view')): ?>
                    <div class="menu-item">
                        <a href="/users" class="menu-link <?= $this->active('/users') ?>">
                            <i class="fas fa-user-friends menu-icon"></i>
                            <span class="menu-text">المستخدمين</span>
                        </a>
                    </div>
                <?php endif; ?>
                
                <!-- Offices (Super Admin Only) -->
                <?php if (hasRole('super_admin')): ?>
                    <div class="menu-item">
                        <a href="/offices" class="menu-link <?= $this->active('/offices') ?>">
                            <i class="fas fa-building menu-icon"></i>
                            <span class="menu-text">المكاتب</span>
                        </a>
                    </div>
                <?php endif; ?>
                
                <!-- Settings -->
                <?php if (can('settings.view')): ?>
                    <div class="menu-item">
                        <a href="/settings" class="menu-link <?= $this->active('/settings') ?>">
                            <i class="fas fa-cog menu-icon"></i>
                            <span class="menu-text">الإعدادات</span>
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>
        
        <!-- System Section (Super Admin Only) -->
        <?php if (hasRole('super_admin')): ?>
            <hr class="my-3">
            <div class="menu-section">
                <small class="text-muted px-3 mb-2 d-block">النظام</small>
                
                <div class="menu-item">
                    <a href="/admin/dashboard" class="menu-link <?= $this->active('/admin') ?>">
                        <i class="fas fa-tools menu-icon"></i>
                        <span class="menu-text">لوحة الإدارة</span>
                    </a>
                </div>
                
                <div class="menu-item">
                    <a href="/admin/logs" class="menu-link <?= $this->active('/admin/logs') ?>">
                        <i class="fas fa-list-alt menu-icon"></i>
                        <span class="menu-text">سجل النشاطات</span>
                    </a>
                </div>
                
                <div class="menu-item">
                    <a href="/admin/backup" class="menu-link <?= $this->active('/admin/backup') ?>">
                        <i class="fas fa-database menu-icon"></i>
                        <span class="menu-text">النسخ الاحتياطي</span>
                    </a>
                </div>
            </div>
        <?php endif; ?>
        
        <!-- Help Section -->
        <hr class="my-3">
        <div class="menu-section">
            <small class="text-muted px-3 mb-2 d-block">المساعدة</small>
            
            <div class="menu-item">
                <a href="/help" class="menu-link">
                    <i class="fas fa-question-circle menu-icon"></i>
                    <span class="menu-text">المساعدة</span>
                </a>
            </div>
            
            <div class="menu-item">
                <a href="/support" class="menu-link">
                    <i class="fas fa-headset menu-icon"></i>
                    <span class="menu-text">الدعم الفني</span>
                </a>
            </div>
        </div>
        
    </nav>
    
    <!-- Sidebar Footer -->
    <div class="sidebar-footer p-3 mt-auto">
        <div class="text-center">
            <small class="text-muted">
                <?= APP_NAME ?> v<?= APP_VERSION ?>
            </small>
        </div>
    </div>
    
</aside>

<!-- Sidebar Overlay for Mobile -->
<div class="sidebar-overlay d-lg-none" onclick="toggleSidebar()"></div>

<script>
function toggleSidebar() {
    $('.sidebar').toggleClass('show');
    $('.sidebar-overlay').toggleClass('show');
}

// Close sidebar when clicking outside on mobile
$(document).on('click', '.sidebar-overlay', function() {
    $('.sidebar').removeClass('show');
    $('.sidebar-overlay').removeClass('show');
});

// Handle sidebar toggle
$('.sidebar-toggle').on('click', function() {
    if (window.innerWidth < 992) {
        $('.sidebar').toggleClass('show');
        $('.sidebar-overlay').toggleClass('show');
    } else {
        $('.sidebar').toggleClass('collapsed');
        $('.main-content').toggleClass('sidebar-collapsed');
    }
});
</script>

<style>
.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1019;
    display: none;
}

.sidebar-overlay.show {
    display: block;
}

@media (max-width: 991.98px) {
    .sidebar {
        transform: translateX(100%);
        z-index: 1020;
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
}
</style>
