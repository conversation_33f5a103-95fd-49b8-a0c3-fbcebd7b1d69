<?php
/**
 * System Configuration File
 * Contains all system-wide settings and constants
 */

// Application Settings
define('APP_NAME', 'نظام إدارة المكاتب العقارية');
define('APP_VERSION', '1.0.0');
define('APP_URL', 'http://localhost/real-estate-saas');
define('APP_ENV', 'development'); // development, production

// Database Configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'real_estate_saas');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');
define('DB_COLLATION', 'utf8mb4_unicode_ci');

// Security Settings
define('JWT_SECRET', 'your-super-secret-jwt-key-change-in-production');
define('JWT_EXPIRY', 3600); // 1 hour
define('JWT_REFRESH_EXPIRY', 604800); // 7 days
define('CSRF_TOKEN_NAME', '_token');
define('SESSION_LIFETIME', 7200); // 2 hours

// File Upload Settings
define('MAX_FILE_SIZE', 5242880); // 5MB
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);
define('ALLOWED_DOCUMENT_TYPES', ['pdf', 'doc', 'docx', 'xls', 'xlsx']);
define('UPLOAD_PATH', ROOT_PATH . '/uploads/');

// Email Configuration
define('MAIL_HOST', 'smtp.gmail.com');
define('MAIL_PORT', 587);
define('MAIL_USERNAME', '<EMAIL>');
define('MAIL_PASSWORD', 'your-app-password');
define('MAIL_FROM_EMAIL', '<EMAIL>');
define('MAIL_FROM_NAME', 'نظام إدارة المكاتب العقارية');

// Pagination Settings
define('ITEMS_PER_PAGE', 20);
define('MAX_PAGINATION_LINKS', 10);

// Currency and Localization
define('DEFAULT_CURRENCY', 'SAR');
define('CURRENCY_SYMBOL', '﷼');
define('DEFAULT_LOCALE', 'ar_SA');
define('DEFAULT_TIMEZONE', 'Asia/Riyadh');

// Tax Settings (Saudi Arabia VAT)
define('DEFAULT_TAX_RATE', 15); // 15% VAT
define('TAX_NUMBER_REQUIRED', true);

// System Roles
define('ROLE_SUPER_ADMIN', 'super_admin');
define('ROLE_OFFICE_OWNER', 'office_owner');
define('ROLE_EMPLOYEE', 'employee');

// Property Status Constants
define('PROPERTY_STATUS_AVAILABLE', 'متاح');
define('PROPERTY_STATUS_SOLD', 'مباع');
define('PROPERTY_STATUS_RENTED', 'مؤجر');
define('PROPERTY_STATUS_NEGOTIATING', 'قيد التفاوض');
define('PROPERTY_STATUS_RESERVED', 'محجوز');

// Property Types
define('PROPERTY_TYPES', [
    'apartment' => 'شقة',
    'villa' => 'فيلا',
    'office' => 'مكتب',
    'shop' => 'محل تجاري',
    'warehouse' => 'مستودع',
    'land' => 'أرض',
    'building' => 'مبنى',
    'farm' => 'مزرعة'
]);

// Client Types
define('CLIENT_TYPES', [
    'buyer' => 'مشتري',
    'seller' => 'بائع',
    'tenant' => 'مستأجر',
    'landlord' => 'مؤجر',
    'investor' => 'مستثمر'
]);

// Invoice Status
define('INVOICE_STATUS_DRAFT', 'مسودة');
define('INVOICE_STATUS_SENT', 'مرسلة');
define('INVOICE_STATUS_PAID', 'مدفوعة');
define('INVOICE_STATUS_OVERDUE', 'متأخرة');
define('INVOICE_STATUS_CANCELLED', 'ملغية');

// Payment Methods
define('PAYMENT_METHODS', [
    'cash' => 'نقداً',
    'bank_transfer' => 'تحويل بنكي',
    'check' => 'شيك',
    'credit_card' => 'بطاقة ائتمان',
    'installments' => 'أقساط'
]);

// Logging Settings
define('LOG_LEVEL', 'INFO'); // DEBUG, INFO, WARNING, ERROR
define('LOG_MAX_SIZE', ********); // 10MB
define('LOG_MAX_FILES', 5);

// Cache Settings
define('CACHE_ENABLED', true);
define('CACHE_LIFETIME', 3600); // 1 hour
define('CACHE_PATH', ROOT_PATH . '/cache/');

// API Settings
define('API_RATE_LIMIT', 100); // requests per minute
define('API_VERSION', 'v1');

// Arabic Date Settings
define('HIJRI_MONTHS', [
    1 => 'محرم', 2 => 'صفر', 3 => 'ربيع الأول', 4 => 'ربيع الثاني',
    5 => 'جمادى الأولى', 6 => 'جمادى الثانية', 7 => 'رجب', 8 => 'شعبان',
    9 => 'رمضان', 10 => 'شوال', 11 => 'ذو القعدة', 12 => 'ذو الحجة'
]);

define('GREGORIAN_MONTHS', [
    1 => 'يناير', 2 => 'فبراير', 3 => 'مارس', 4 => 'أبريل',
    5 => 'مايو', 6 => 'يونيو', 7 => 'يوليو', 8 => 'أغسطس',
    9 => 'سبتمبر', 10 => 'أكتوبر', 11 => 'نوفمبر', 12 => 'ديسمبر'
]);

define('ARABIC_DAYS', [
    'Sunday' => 'الأحد',
    'Monday' => 'الإثنين',
    'Tuesday' => 'الثلاثاء',
    'Wednesday' => 'الأربعاء',
    'Thursday' => 'الخميس',
    'Friday' => 'الجمعة',
    'Saturday' => 'السبت'
]);

// Error Messages in Arabic
define('ERROR_MESSAGES', [
    'required' => 'هذا الحقل مطلوب',
    'email' => 'يرجى إدخال بريد إلكتروني صحيح',
    'min_length' => 'يجب أن يكون الحد الأدنى %d أحرف',
    'max_length' => 'يجب أن لا يتجاوز %d حرف',
    'numeric' => 'يجب أن يكون رقماً',
    'phone' => 'يرجى إدخال رقم هاتف صحيح',
    'unique' => 'هذه القيمة مستخدمة مسبقاً',
    'file_size' => 'حجم الملف كبير جداً',
    'file_type' => 'نوع الملف غير مدعوم',
    'unauthorized' => 'غير مصرح لك بالوصول',
    'not_found' => 'العنصر غير موجود',
    'server_error' => 'خطأ في الخادم'
]);

// Success Messages in Arabic
define('SUCCESS_MESSAGES', [
    'created' => 'تم الإنشاء بنجاح',
    'updated' => 'تم التحديث بنجاح',
    'deleted' => 'تم الحذف بنجاح',
    'login' => 'تم تسجيل الدخول بنجاح',
    'logout' => 'تم تسجيل الخروج بنجاح',
    'email_sent' => 'تم إرسال البريد الإلكتروني بنجاح',
    'password_reset' => 'تم إعادة تعيين كلمة المرور بنجاح'
]);

// System Permissions
define('PERMISSIONS', [
    'properties' => [
        'view' => 'عرض العقارات',
        'create' => 'إضافة عقار',
        'edit' => 'تعديل عقار',
        'delete' => 'حذف عقار',
        'export' => 'تصدير العقارات'
    ],
    'clients' => [
        'view' => 'عرض العملاء',
        'create' => 'إضافة عميل',
        'edit' => 'تعديل عميل',
        'delete' => 'حذف عميل',
        'export' => 'تصدير العملاء'
    ],
    'invoices' => [
        'view' => 'عرض الفواتير',
        'create' => 'إنشاء فاتورة',
        'edit' => 'تعديل فاتورة',
        'delete' => 'حذف فاتورة',
        'print' => 'طباعة فاتورة'
    ],
    'reports' => [
        'view' => 'عرض التقارير',
        'export' => 'تصدير التقارير'
    ],
    'users' => [
        'view' => 'عرض المستخدمين',
        'create' => 'إضافة مستخدم',
        'edit' => 'تعديل مستخدم',
        'delete' => 'حذف مستخدم'
    ],
    'settings' => [
        'view' => 'عرض الإعدادات',
        'edit' => 'تعديل الإعدادات'
    ]
]);

// Load environment-specific configuration
if (file_exists(CONFIG_PATH . '/env.php')) {
    require_once CONFIG_PATH . '/env.php';
}
